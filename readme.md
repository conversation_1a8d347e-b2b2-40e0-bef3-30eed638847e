KOKUME
=================
Kokume je strategická hra využívající mapové podklady OpenStreetMap. Hráči se snaží ovládnout území a udržet si ho. Souboje o území probíhají v minihrách v reálném čase a hráči mohou využívat různé strategie a taktiky. Hra je inspirována hrou Ingress a dalšími podobnými hrami.

Instalace
------------

```bash
make install
make init
```

 - Změňte parametry databáze v ``/config/local.neon``

#### Inicializace databáze a sestavení JS
```bash
make build
```

#### Generování OAuth kl<PERSON>
```bash
# privátní klíč
openssl genrsa -aes128 -passout pass:_keyPassPhrase_ -out resources/oauth/private.key 2048

# extrakce veřejného klíče z privátního klíče
openssl rsa -in resources/oauth/private.key -passin pass:_keyPassPhrase_ -pubout -out resources/oauth/public.key

# generování encryptionKey
php -r 'echo base64_encode(random_bytes(32)), PHP_EOL;'
```
**encryptionKey** a ``_keyPassPhrase_`` by měly být umístěny v ``/config/local.neon``


#### Spuštění webového serveru (http://127.0.0.1:8000)
```bash
make dev
```

#### 🌳 Struktura
 - app
   - API - REST API
   - Console - konzolové příkazy
   - Domain - business logika a doménově specifické třídy
   - Model - aplikační páteř
   - UI - presentery, komponenty, formuláře, šablony
   - Bootstrap.php - Nette vstupní bod
 - assets - js, css, obrázky
 - bin - konzolový vstupní bod (bin/console)
 - config - konfigurační soubory
   - app - aplikační konfigurace
   - env - prod/dev/test prostředí
   - ext - konfigurace rozšíření
   - local.example.neon - šablona pro lokální konfiguraci
   - local.neon - lokální runtime konfigurace
 - db - databázové soubory
   - Fixtures - PHP fixtures
   - Migrations - migrační soubory
 - node_modules - npm balíčky
 - resources - statický obsah pro tracy, maily a další
 - var - runtime soubory
   - temp - dočasné soubory a cache
   - log - runtime a error logy
 - vendor - složka composeru
 - www - veřejný obsah

#### 🗺️ Import OSM dat
 - náhled dat: https://overpass-turbo.eu/s/1MYy
```bash
./scripts/import_overpass_data.sh
```

#### 📱 Dotykové ovládání mapy (Godot verze)

Godot verze hry podporuje pokročilé dotykové ovládání optimalizované pro mobilní telefony:

**Základní ovládání:**
- **Jednoduchý dotek**: Kliknutí na objekty na mapě (budovy, předměty, regiony)
- **Tažení jedním prstem**: Posouvání mapy
- **Momentum scrolling**: Plynulé doběhnutí při rychlém tažení

**Pokročilá gesta:**
- **Pinch-to-zoom**: Přiblížení/oddálení pomocí dvou prstů
- **Two-finger pan**: Posouvání mapy dvěma prsty
- **Magnify gesture**: Systémové gesto pro zoom (iOS/Android)
- **Pan gesture**: Systémové gesto pro posouvání

**Technické detaily:**
- Implementováno v `game/scripts/map/Map.gd`
- Podporuje multi-touch až pro 10 současných dotyků
- Automatické rozpoznání gest vs. kliknutí
- Optimalizováno pro 60 FPS na mobilních zařízeních
- Momentum scrolling s fyzikálním třením

**Testování:**
```bash
# Spuštění testu dotykového ovládání
godot --path game --script test_touch_controls.gd
```
