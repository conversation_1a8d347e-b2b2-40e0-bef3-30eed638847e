# Extension > Contributte
#
extensions:
	console: Contributte\Console\DI\ConsoleExtension(%consoleMode%)
	events: Contributte\EventDispatcher\DI\EventDispatcherExtension
	events2nette: Contributte\Events\Extra\DI\EventBridgesExtension
	webpack: Contributte\Webpack\DI\WebpackExtension(%debugMode%, %consoleMode%)
	scheduler: Contributte\Scheduler\DI\SchedulerExtension
	sentry: Contributte\Sentry\DI\SentryExtension

console:
	url: http://kokume.loc/

events:
	debug: %debugMode%

webpack:
	build:
		directory: %wwwDir%/dist
		publicPath: /
	manifest:
		name: manifest.json
	devServer:
		enabled: %debugMode% # default
		url: http://localhost:3000
		timeout: 0.1 # (seconds) default

scheduler:
	path: '%tempDir%/scheduler'
	jobs:
		buildingJob: App\Scheduler\BuildingJob
		buildingProductionJob: App\Scheduler\BuildingProductionJob
		itemSpawningJob: App\Scheduler\ItemSpawningJob
		unitProductionJob: App\Scheduler\UnitProductionJob
		buildingUnitProductionJob: App\Scheduler\BuildingUnitProductionJob
sentry:
	# Enabled only on production
	enable: %sentry.enable%

	# Client configuration
	client:
		dsn: %sentry.dns%
		traces_sample_rate: 1.0
		integrations:
			- Contributte\Sentry\Integration\NetteApplicationIntegration()
			- Contributte\Sentry\Integration\NetteHttpIntegration()
			- Contributte\Sentry\Integration\NetteSecurityIntegration()
			- Contributte\Sentry\Integration\NetteSessionIntegration()
#			- Contributte\Sentry\Integration\ExtraIntegration([
#				version: %appVersion%
#			])
			- Contributte\Sentry\Integration\IgnoreErrorIntegration([
				ignore_exception_regex: [
					'/Deprecated (.*)/'
				],
				ignore_message_regex: [
					'/PHP Deprecated (.*)/'
				],
			])

services:
	# Sentry Performance Monitor
	- Contributte\Sentry\Performance\NetteApplicationMonitor
	application.application:
		setup:
			- @Contributte\Sentry\Performance\NetteApplicationMonitor::hook(@self, 'onRequest')
			- @Contributte\Sentry\Performance\NetteApplicationMonitor::hook(@self, 'onShutdown')
