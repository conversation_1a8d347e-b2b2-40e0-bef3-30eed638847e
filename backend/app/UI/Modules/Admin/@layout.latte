<!DOCTYPE html>
<html lang="cs">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">

	<link rel="icon" type="image/x-icon" href="{webpack images/minified/logo.webp}">

	<title>{ifset title}{include title|stripHtml} | {/ifset}Kokume</title>

	{block head}
		<link rel="stylesheet" href="{webpack admin.css}">
		<link rel="stylesheet" href="{webpack datagrid.css}">
		<link rel="stylesheet" href="{webpack fontawesome.css}">
	{/block}
</head>

<body n:block="layout">
	<header class="navbar sticky-top bg-dark flex-md-nowrap p-0 shadow" data-bs-theme="dark">
		<a class="navbar-brand col-md-3 col-lg-2 me-0 px-3 fs-6 text-white" href="#">Kokume</a>

		<ul class="navbar-nav flex-row d-md-none">
			<li class="nav-item text-nowrap">
				<button class="nav-link px-3 text-white" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
					<span class="navbar-toggler-icon"></span>
				</button>
			</li>
		</ul>
	</header>

	<div class="container-fluid">
		<div class="row">
			<div class="sidebar border border-right col-md-3 col-lg-2 p-0 bg-body-tertiary">
				<div class="offcanvas-md offcanvas-end bg-body-tertiary" tabindex="-1" id="sidebarMenu" aria-labelledby="sidebarMenuLabel">
					<div class="offcanvas-header">
						<h5 class="offcanvas-title" id="sidebarMenuLabel">Kokume</h5>
						<button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#sidebarMenu" aria-label="Close"></button>
					</div>
					<div class="offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto">
						<ul class="nav flex-column">
							<li class="nav-item">
								<a class="nav-link d-flex align-items-center gap-2 active" aria-current="page" n:href="Dashboard:">
									Přehled
								</a>
							</li>
							<li class="nav-item">
								<a class="nav-link d-flex align-items-center gap-2" n:href="BuildingType:list">
									Typy budov
								</a>
							</li>
							<li class="nav-item">
								<a class="nav-link d-flex align-items-center gap-2" n:href="ItemDefinition:list">
									Definice itemů
								</a>
							</li>
							<li class="nav-item">
								<a class="nav-link d-flex align-items-center gap-2" n:href="Player:list">
									Hráči
								</a>
							</li>
                            <li class="nav-item">
                                <a class="nav-link d-flex align-items-center gap-2" n:href="UnitDefinition:list">
                                    Definice jednotek
                                </a>
                            </li>
						</ul>

						<hr class="my-3">

						<ul class="nav flex-column mb-auto">
							<li class="nav-item">
								<a class="nav-link d-flex align-items-center gap-2" n:href="Settings:">
									Nastavení
								</a>
							</li>
						</ul>

						<hr class="my-3">

						<ul class="nav flex-column mb-auto">
							<li class="nav-item">
								<div class="nav-link text-black">
									Přihlášený uživatel:
									<strong>{$user->getIdentity()->getData()['email']}</strong>
								</div>
							</li>
							<li class="nav-item">
								<a class="nav-link d-flex align-items-center gap-2" n:href="Sign:out">
									Odhlásit se
								</a>
							</li>
						</ul>
					</div>
				</div>
			</div>
			<main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-2">
				{control breadcrumb}
				{include content}
			</main>
		</div>
	</div>

	{block scripts}
		<script src="{webpack admin.js}"></script>
		<script src="{webpack datagrid.js}"></script>
	{/block}
</body>
</html>
