/*! For license information please see datagrid.5a83018c.bundle.js.LICENSE.txt */
!function(){var e={289:function(e,t,n){"use strict";n.d(t,{Ay:function(){return y}});const r=e=>{"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e):e()};class i extends Error{}const o=(e,t)=>{if(!e)throw new i("Assertion failed"+(void 0!==t?`: ${t}`:"."))};class a extends EventTarget{constructor(e){super(),this.naja=e,this.selector=".ajax",this.allowedOrigins=[window.location.origin],this.handler=this.handleUI.bind(this),e.addEventListener("init",this.initialize.bind(this))}initialize(){r((()=>this.bindUI(window.document.body))),this.naja.snippetHandler.addEventListener("afterUpdate",(e=>{const{snippet:t}=e.detail;this.bindUI(t)}))}bindUI(e){const t=[`a${this.selector}`,`input[type="submit"]${this.selector}`,`input[type="image"]${this.selector}`,`button[type="submit"]${this.selector}`,`form${this.selector} input[type="submit"]`,`form${this.selector} input[type="image"]`,`form${this.selector} button[type="submit"]`].join(", "),n=e=>{e.removeEventListener("click",this.handler),e.addEventListener("click",this.handler)},r=e.querySelectorAll(t);for(let e=0;e<r.length;e++)n(r.item(e));e.matches(t)&&n(e);const i=e=>{e.removeEventListener("submit",this.handler),e.addEventListener("submit",this.handler)};e.matches(`form${this.selector}`)&&i(e);const o=e.querySelectorAll(`form${this.selector}`);for(let e=0;e<o.length;e++)i(o.item(e))}handleUI(e){const t=e;if(t.altKey||t.ctrlKey||t.shiftKey||t.metaKey||t.button)return;const n=e.currentTarget,r=this.naja.prepareOptions(),i=()=>{};"submit"===e.type?this.submitForm(n,r,e).catch(i):"click"===e.type&&this.clickElement(n,r,t).catch(i)}async clickElement(e,t={},n){let r,i="GET",a="";if(!this.dispatchEvent(new CustomEvent("interaction",{cancelable:!0,detail:{element:e,originalEvent:n,options:t}})))return n?.preventDefault(),{};if("A"===e.tagName)o(e instanceof HTMLAnchorElement),i="GET",a=e.href,r=null;else if("INPUT"===e.tagName||"BUTTON"===e.tagName){o(e instanceof HTMLInputElement||e instanceof HTMLButtonElement);const{form:t}=e;if(i=e.getAttribute("formmethod")?.toUpperCase()??t?.getAttribute("method")?.toUpperCase()??"GET",a=e.getAttribute("formaction")??t?.getAttribute("action")??window.location.pathname+window.location.search,r=new FormData(t??void 0),"submit"===e.type&&""!==e.name)r.append(e.name,e.value||"");else if("image"===e.type){const t=e.getBoundingClientRect(),i=""!==e.name?`${e.name}.`:"";r.append(`${i}x`,Math.max(0,Math.floor(void 0!==n?n.pageX-t.left:0))),r.append(`${i}y`,Math.max(0,Math.floor(void 0!==n?n.pageY-t.top:0)))}}if(!this.isUrlAllowed(a))throw new Error(`Cannot dispatch async request, URL is not allowed: ${a}`);return n?.preventDefault(),this.naja.makeRequest(i,a,r,t)}async submitForm(e,t={},n){if(!this.dispatchEvent(new CustomEvent("interaction",{cancelable:!0,detail:{element:e,originalEvent:n,options:t}})))return n?.preventDefault(),{};const r=e.getAttribute("method")?.toUpperCase()??"GET",i=e.getAttribute("action")??window.location.pathname+window.location.search,o=new FormData(e);if(!this.isUrlAllowed(i))throw new Error(`Cannot dispatch async request, URL is not allowed: ${i}`);return n?.preventDefault(),this.naja.makeRequest(r,i,o,t)}isUrlAllowed(e){const t=new URL(e,location.href);return"null"!==t.origin&&this.allowedOrigins.includes(t.origin)}}class s{constructor(e){this.naja=e,e.addEventListener("init",this.initialize.bind(this)),e.uiHandler.addEventListener("interaction",this.processForm.bind(this))}initialize(){r((()=>this.initForms(window.document.body))),this.naja.snippetHandler.addEventListener("afterUpdate",(e=>{const{snippet:t}=e.detail;this.initForms(t)}))}initForms(e){const t=this.netteForms||window.Nette;if(t){"form"===e.tagName&&t.initForm(e);const n=e.querySelectorAll("form");for(let e=0;e<n.length;e++)t.initForm(n.item(e))}}processForm(e){const{element:t,originalEvent:n}=e.detail,r=t;void 0!==r.form&&null!==r.form&&(r.form["nette-submittedBy"]=t);const i=this.netteForms||window.Nette;"FORM"!==t.tagName&&!t.form||!i||i.validateForm(t)||(n&&(n.stopImmediatePropagation(),n.preventDefault()),e.preventDefault())}}class l extends EventTarget{constructor(e){super(),this.naja=e,e.uiHandler.addEventListener("interaction",(e=>{const{element:t,options:n}=e.detail;if(t&&(t.hasAttribute("data-naja-force-redirect")||t.form?.hasAttribute("data-naja-force-redirect"))){const e=t.getAttribute("data-naja-force-redirect")??t.form?.getAttribute("data-naja-force-redirect");n.forceRedirect="off"!==e}})),e.addEventListener("success",(e=>{const{payload:t,options:n}=e.detail;t.redirect&&(this.makeRedirect(t.redirect,n.forceRedirect??!1,n),e.stopImmediatePropagation())})),this.locationAdapter={assign:e=>window.location.assign(e)}}makeRedirect(e,t,n={}){e instanceof URL&&(e=e.href);let r=t||!this.naja.uiHandler.isUrlAllowed(e);this.dispatchEvent(new CustomEvent("redirect",{cancelable:!0,detail:{url:e,setUrl(t){e=t},isHardRedirect:r,setHardRedirect(e){r=!!e},options:n}}))&&(r?this.locationAdapter.assign(e):this.naja.makeRequest("GET",e,null,n))}}class c extends EventTarget{constructor(e){super(),this.naja=e,this.op={replace:(e,t)=>{e.innerHTML=t},prepend:(e,t)=>e.insertAdjacentHTML("afterbegin",t),append:(e,t)=>e.insertAdjacentHTML("beforeend",t)},e.addEventListener("success",(e=>{const{options:t,payload:n}=e.detail;n.snippets&&this.updateSnippets(n.snippets,!1,t)}))}static findSnippets(e){const t={},n=window.document.querySelectorAll('[id^="snippet-"]');for(let r=0;r<n.length;r++){const i=n.item(r);(e?.(i)??1)&&(t[i.id]=i.innerHTML)}return t}updateSnippets(e,t=!1,n={}){Object.keys(e).forEach((r=>{const i=document.getElementById(r);i&&this.updateSnippet(i,e[r],t,n)}))}updateSnippet(e,t,n,r){let i=this.op.replace;!e.hasAttribute("data-naja-snippet-prepend")&&!e.hasAttribute("data-ajax-prepend")||n?!e.hasAttribute("data-naja-snippet-append")&&!e.hasAttribute("data-ajax-append")||n||(i=this.op.append):i=this.op.prepend,this.dispatchEvent(new CustomEvent("beforeUpdate",{cancelable:!0,detail:{snippet:e,content:t,fromCache:n,operation:i,changeOperation(e){i=e},options:r}}))&&("title"===e.tagName.toLowerCase()?document.title=t:i(e,t),this.dispatchEvent(new CustomEvent("afterUpdate",{cancelable:!0,detail:{snippet:e,content:t,fromCache:n,operation:i,options:r}})))}}class u extends EventTarget{constructor(e){super(),this.naja=e,this.initialized=!1,this.cursor=0,this.popStateHandler=this.handlePopState.bind(this),e.addEventListener("init",this.initialize.bind(this)),e.addEventListener("before",this.saveUrl.bind(this)),e.addEventListener("before",this.replaceInitialState.bind(this)),e.addEventListener("success",this.pushNewState.bind(this)),e.redirectHandler.addEventListener("redirect",this.saveRedirectedUrl.bind(this)),e.uiHandler.addEventListener("interaction",this.configureMode.bind(this)),this.historyAdapter={replaceState:(e,t,n)=>window.history.replaceState(e,t,n),pushState:(e,t,n)=>window.history.pushState(e,t,n)}}set uiCache(e){console.warn("Naja: HistoryHandler.uiCache is deprecated, use options.snippetCache instead."),this.naja.defaultOptions.snippetCache=e}handlePopState(e){const{state:t}=e;if("naja"!==t?.source)return;const n=t.cursor-this.cursor;this.cursor=t.cursor;const r=this.naja.prepareOptions();this.dispatchEvent(new CustomEvent("restoreState",{detail:{state:t,direction:n,options:r}}))}initialize(){window.addEventListener("popstate",this.popStateHandler)}saveUrl(e){const{url:t,options:n}=e.detail;n.href??=t}saveRedirectedUrl(e){const{url:t,options:n}=e.detail;n.href=t}replaceInitialState(e){const{options:t}=e.detail;!1===u.normalizeMode(t.history)||this.initialized||(r((()=>this.historyAdapter.replaceState(this.buildState(window.location.href,"replace",this.cursor,t),window.document.title,window.location.href))),this.initialized=!0)}configureMode(e){const{element:t,options:n}=e.detail;if(t&&(t.hasAttribute("data-naja-history")||t.form?.hasAttribute("data-naja-history"))){const e=t.getAttribute("data-naja-history")??t.form?.getAttribute("data-naja-history");n.history=u.normalizeMode(e)}}static normalizeMode(e){return"off"!==e&&!1!==e&&("replace"!==e||"replace")}pushNewState(e){const{payload:t,options:n}=e.detail,r=u.normalizeMode(n.history);if(!1===r)return;t.postGet&&t.url&&(n.href=t.url);const i="replace"===r?"replaceState":"pushState",o="replace"===r?this.cursor:++this.cursor;this.historyAdapter[i](this.buildState(n.href,r,o,n),window.document.title,n.href)}buildState(e,t,n,r){const i={source:"naja",cursor:n,href:e};return this.dispatchEvent(new CustomEvent("buildState",{detail:{state:i,operation:"replace"===t?"replaceState":"pushState",options:r}})),i}}class d extends EventTarget{constructor(e){super(),this.naja=e,this.storages={off:new p(e),history:new f,session:new h},e.uiHandler.addEventListener("interaction",this.configureCache.bind(this)),e.historyHandler.addEventListener("buildState",this.buildHistoryState.bind(this)),e.historyHandler.addEventListener("restoreState",this.restoreHistoryState.bind(this))}resolveStorage(e){let t;return t=!0===e||void 0===e?"history":!1===e?"off":e,this.storages[t]}configureCache(e){const{element:t,options:n}=e.detail;if(t&&(t.hasAttribute("data-naja-snippet-cache")||t.form?.hasAttribute("data-naja-snippet-cache")||t.hasAttribute("data-naja-history-cache")||t.form?.hasAttribute("data-naja-history-cache"))){const e=t.getAttribute("data-naja-snippet-cache")??t.form?.getAttribute("data-naja-snippet-cache")??t.getAttribute("data-naja-history-cache")??t.form?.getAttribute("data-naja-history-cache");n.snippetCache=e}}buildHistoryState(e){const{state:t,options:n}=e.detail;"historyUiCache"in n&&(console.warn("Naja: options.historyUiCache is deprecated, use options.snippetCache instead."),n.snippetCache=n.historyUiCache);const r=c.findSnippets((e=>!(e.hasAttribute("data-naja-history-nocache")||e.hasAttribute("data-history-nocache")||e.hasAttribute("data-naja-snippet-cache")&&"off"===e.getAttribute("data-naja-snippet-cache"))));if(!this.dispatchEvent(new CustomEvent("store",{cancelable:!0,detail:{snippets:r,state:t,options:n}})))return;const i=this.resolveStorage(n.snippetCache);t.snippets={storage:i.type,key:i.store(r)}}restoreHistoryState(e){const{state:t,options:n}=e.detail;if(void 0===t.snippets)return;if(n.snippetCache=t.snippets.storage,!this.dispatchEvent(new CustomEvent("fetch",{cancelable:!0,detail:{state:t,options:n}})))return;const r=this.resolveStorage(n.snippetCache).fetch(t.snippets.key,t,n);null!==r&&this.dispatchEvent(new CustomEvent("restore",{cancelable:!0,detail:{snippets:r,state:t,options:n}}))&&this.naja.snippetHandler.updateSnippets(r,!0,n)}}class p{constructor(e){this.naja=e,this.type="off"}store(){return null}fetch(e,t,n){return this.naja.makeRequest("GET",t.href,null,{...n,history:!1,snippetCache:!1}),null}}class f{constructor(){this.type="history"}store(e){return e}fetch(e){return e}}class h{constructor(){this.type="session"}store(e){const t=Math.random().toString(36).substring(2,8);return window.sessionStorage.setItem(t,JSON.stringify(e)),t}fetch(e){const t=window.sessionStorage.getItem(e);return null===t?null:JSON.parse(t)}}class g{constructor(e){this.loadedScripts=new Set,e.addEventListener("init",(()=>{r((()=>{document.querySelectorAll("script[data-naja-script-id]").forEach((e=>{const t=e.getAttribute("data-naja-script-id");null!==t&&""!==t&&this.loadedScripts.add(t)}))})),e.snippetHandler.addEventListener("afterUpdate",(e=>{const{content:t}=e.detail;this.loadScripts(t)}))}))}loadScripts(e){"string"!=typeof e?Object.keys(e).forEach((t=>{const n=e[t];this.loadScriptsInSnippet(n)})):this.loadScriptsInSnippet(e)}loadScriptsInSnippet(e){if(!/<script/i.test(e))return;const t=window.document.createElement("div");t.innerHTML=e;const n=t.querySelectorAll("script");for(let e=0;e<n.length;e++){const t=n.item(e),r=t.getAttribute("data-naja-script-id");if(null!==r&&""!==r&&this.loadedScripts.has(r))continue;const i=window.document.createElement("script");if(i.innerHTML=t.innerHTML,t.hasAttributes()){const e=t.attributes;for(let t=0;t<e.length;t++){const n=e[t].name;i.setAttribute(n,e[t].value)}}window.document.head.appendChild(i).parentNode.removeChild(i),null!==r&&""!==r&&this.loadedScripts.add(r)}}}class m extends EventTarget{constructor(e,t,n,r,i,o,p){super(),this.VERSION=2,this.initialized=!1,this.extensions=[],this.defaultOptions={},this.uiHandler=new(e??a)(this),this.redirectHandler=new(t??l)(this),this.snippetHandler=new(n??c)(this),this.formsHandler=new(r??s)(this),this.historyHandler=new(i??u)(this),this.snippetCache=new(o??d)(this),this.scriptLoader=new(p??g)(this)}registerExtension(e){this.initialized&&e.initialize(this),this.extensions.push(e)}initialize(e={}){if(this.initialized)throw new Error("Cannot initialize Naja, it is already initialized.");this.defaultOptions=this.prepareOptions(e),this.extensions.forEach((e=>e.initialize(this))),this.dispatchEvent(new CustomEvent("init",{detail:{defaultOptions:this.defaultOptions}})),this.initialized=!0}prepareOptions(e){return{...this.defaultOptions,...e,fetch:{...this.defaultOptions.fetch,...e?.fetch}}}async makeRequest(e,t,n=null,r={}){"string"==typeof t&&(t=new URL(t,location.href)),r=this.prepareOptions(r);const i=new Headers(r.fetch.headers||{}),o=this.transformData(t,e,n),a=new AbortController,s=new Request(t.toString(),{credentials:"same-origin",...r.fetch,method:e,headers:i,body:o,signal:a.signal});if(s.headers.set("X-Requested-With","XMLHttpRequest"),s.headers.set("Accept","application/json"),!this.dispatchEvent(new CustomEvent("before",{cancelable:!0,detail:{request:s,method:e,url:t.toString(),data:n,options:r}})))return{};const l=window.fetch(s);let c,u;this.dispatchEvent(new CustomEvent("start",{detail:{request:s,promise:l,abortController:a,options:r}}));try{if(c=await l,!c.ok)throw new v(c);u=await c.json()}catch(e){if("AbortError"===e.name)return this.dispatchEvent(new CustomEvent("abort",{detail:{request:s,error:e,options:r}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:s,response:c,payload:void 0,error:e,options:r}})),{};throw this.dispatchEvent(new CustomEvent("error",{detail:{request:s,response:c,error:e,options:r}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:s,response:c,payload:void 0,error:e,options:r}})),e}return this.dispatchEvent(new CustomEvent("payload",{detail:{request:s,response:c,payload:u,options:r}})),this.dispatchEvent(new CustomEvent("success",{detail:{request:s,response:c,payload:u,options:r}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:s,response:c,payload:u,error:void 0,options:r}})),u}appendToQueryString(e,t,n){if(null!=n)if(Array.isArray(n)||Object.getPrototypeOf(n)===Object.prototype)for(const[r,i]of Object.entries(n))this.appendToQueryString(e,`${t}[${r}]`,i);else e.append(t,String(n))}transformData(e,t,n){const r=["GET","HEAD"].includes(t.toUpperCase());if(r&&n instanceof FormData){for(const[t,r]of n)null!=r&&e.searchParams.append(t,String(r));return null}if(null!==n&&Object.getPrototypeOf(n)===Object.prototype||Array.isArray(n)){const t=r?e.searchParams:new URLSearchParams;for(const[e,r]of Object.entries(n))this.appendToQueryString(t,e,r);return r?null:t}return n}}class v extends Error{constructor(e){const t=`HTTP ${e.status}: ${e.statusText}`;super(t),this.name=this.constructor.name,this.stack=new Error(t).stack,this.response=e}}const y=new m;y.registerExtension(new class{constructor(){this.abortControllers=new Set}initialize(e){e.uiHandler.addEventListener("interaction",this.checkAbortable.bind(this)),e.addEventListener("init",this.onInitialize.bind(this)),e.addEventListener("start",this.saveAbortController.bind(this)),e.addEventListener("complete",this.removeAbortController.bind(this))}onInitialize(){document.addEventListener("keydown",(e=>{if("Escape"===e.key&&!(e.ctrlKey||e.shiftKey||e.altKey||e.metaKey)){for(const e of this.abortControllers)e.abort();this.abortControllers.clear()}}))}checkAbortable(e){const{element:t,options:n}=e.detail;(t.hasAttribute("data-naja-abort")||t.form?.hasAttribute("data-naja-abort"))&&(n.abort="off"!==(t.getAttribute("data-naja-abort")??t.form?.getAttribute("data-naja-abort")))}saveAbortController(e){const{abortController:t,options:n}=e.detail;!1!==n.abort&&(this.abortControllers.add(t),n.clearAbortExtension=()=>this.abortControllers.delete(t))}removeAbortController(e){const{options:t}=e.detail;!1!==t.abort&&t.clearAbortExtension&&t.clearAbortExtension()}}),y.registerExtension(new class{constructor(){this.abortControllers=new Map}initialize(e){e.uiHandler.addEventListener("interaction",this.checkUniqueness.bind(this)),e.addEventListener("start",this.abortPreviousRequest.bind(this)),e.addEventListener("complete",this.clearRequest.bind(this))}checkUniqueness(e){const{element:t,options:n}=e.detail;if(t.hasAttribute("data-naja-unique")??t.form?.hasAttribute("data-naja-unique")){const e=t.getAttribute("data-naja-unique")??t.form?.getAttribute("data-naja-unique");n.unique="off"!==e&&(e??"default")}}abortPreviousRequest(e){const{abortController:t,options:n}=e.detail;!1!==n.unique&&(this.abortControllers.get(n.unique??"default")?.abort(),this.abortControllers.set(n.unique??"default",t))}clearRequest(e){const{request:t,options:n}=e.detail;t.signal.aborted||!1===n.unique||this.abortControllers.delete(n.unique??"default")}})},616:function(e,t){var n;!function(t,n){"use strict";"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,(function(r,i){"use strict";var o=[],a=Object.getPrototypeOf,s=o.slice,l=o.flat?function(e){return o.flat.call(e)}:function(e){return o.concat.apply([],e)},c=o.push,u=o.indexOf,d={},p=d.toString,f=d.hasOwnProperty,h=f.toString,g=h.call(Object),m={},v=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},y=function(e){return null!=e&&e===e.window},b=r.document,x={type:!0,src:!0,nonce:!0,noModule:!0};function w(e,t,n){var r,i,o=(n=n||b).createElement("script");if(o.text=e,t)for(r in x)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function E(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?d[p.call(e)]||"object":typeof e}var C="3.7.1",T=/HTML$/i,S=function(e,t){return new S.fn.init(e,t)};function A(e){var t=!!e&&"length"in e&&e.length,n=E(e);return!v(e)&&!y(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function k(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}S.fn=S.prototype={jquery:C,constructor:S,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=S.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return S.each(this,e)},map:function(e){return this.pushStack(S.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(S.grep(this,(function(e,t){return(t+1)%2})))},odd:function(){return this.pushStack(S.grep(this,(function(e,t){return t%2})))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:o.sort,splice:o.splice},S.extend=S.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[s]||{},s++),"object"==typeof a||v(a)||(a={}),s===l&&(a=this,s--);s<l;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(c&&r&&(S.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||S.isPlainObject(n)?n:{},i=!1,a[t]=S.extend(c,o,r)):void 0!==r&&(a[t]=r));return a},S.extend({expando:"jQuery"+(C+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==p.call(e)||(t=a(e))&&("function"!=typeof(n=f.call(t,"constructor")&&t.constructor)||h.call(n)!==g))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){w(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(A(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)for(;t=e[r++];)n+=S.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(A(Object(e))?S.merge(n,"string"==typeof e?[e]:e):c.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:u.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!T.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!==a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(A(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return l(a)},guid:1,support:m}),"function"==typeof Symbol&&(S.fn[Symbol.iterator]=o[Symbol.iterator]),S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){d["[object "+t+"]"]=t.toLowerCase()}));var j=o.pop,L=o.sort,D=o.splice,q="[\\x20\\t\\r\\n\\f]",N=new RegExp("^"+q+"+|((?:^|[^\\\\])(?:\\\\.)*)"+q+"+$","g");S.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var H=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function _(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}S.escapeSelector=function(e){return(e+"").replace(H,_)};var O=b,M=c;!function(){var e,t,n,i,a,l,c,d,p,h,g=M,v=S.expando,y=0,b=0,x=ee(),w=ee(),E=ee(),C=ee(),T=function(e,t){return e===t&&(a=!0),0},A="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",H="(?:\\\\[\\da-fA-F]{1,6}"+q+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",_="\\["+q+"*("+H+")(?:"+q+"*([*^$|!~]?=)"+q+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+H+"))|)"+q+"*\\]",R=":("+H+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+_+")*)|.*)\\)|)",P=new RegExp(q+"+","g"),I=new RegExp("^"+q+"*,"+q+"*"),F=new RegExp("^"+q+"*([>+~]|"+q+")"+q+"*"),$=new RegExp(q+"|>"),U=new RegExp(R),W=new RegExp("^"+H+"$"),z={ID:new RegExp("^#("+H+")"),CLASS:new RegExp("^\\.("+H+")"),TAG:new RegExp("^("+H+"|[*])"),ATTR:new RegExp("^"+_),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+q+"*(even|odd|(([+-]|)(\\d*)n|)"+q+"*(?:([+-]|)"+q+"*(\\d+)|))"+q+"*\\)|)","i"),bool:new RegExp("^(?:"+A+")$","i"),needsContext:new RegExp("^"+q+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+q+"*((?:-\\d)?\\d*)"+q+"*\\)|)(?=[^-]|$)","i")},B=/^(?:input|select|textarea|button)$/i,X=/^h\d$/i,G=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,V=/[+~]/,K=new RegExp("\\\\[\\da-fA-F]{1,6}"+q+"?|\\\\([^\\r\\n\\f])","g"),Q=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},Y=function(){le()},J=pe((function(e){return!0===e.disabled&&k(e,"fieldset")}),{dir:"parentNode",next:"legend"});try{g.apply(o=s.call(O.childNodes),O.childNodes),o[O.childNodes.length].nodeType}catch(e){g={apply:function(e,t){M.apply(e,s.call(t))},call:function(e){M.apply(e,s.call(arguments,1))}}}function Z(e,t,n,r){var i,o,a,s,c,u,f,h=t&&t.ownerDocument,y=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==y&&9!==y&&11!==y)return n;if(!r&&(le(t),t=t||l,d)){if(11!==y&&(c=G.exec(e)))if(i=c[1]){if(9===y){if(!(a=t.getElementById(i)))return n;if(a.id===i)return g.call(n,a),n}else if(h&&(a=h.getElementById(i))&&Z.contains(t,a)&&a.id===i)return g.call(n,a),n}else{if(c[2])return g.apply(n,t.getElementsByTagName(e)),n;if((i=c[3])&&t.getElementsByClassName)return g.apply(n,t.getElementsByClassName(i)),n}if(!(C[e+" "]||p&&p.test(e))){if(f=e,h=t,1===y&&($.test(e)||F.test(e))){for((h=V.test(e)&&se(t.parentNode)||t)==t&&m.scope||((s=t.getAttribute("id"))?s=S.escapeSelector(s):t.setAttribute("id",s=v)),o=(u=ue(e)).length;o--;)u[o]=(s?"#"+s:":scope")+" "+de(u[o]);f=u.join(",")}try{return g.apply(n,h.querySelectorAll(f)),n}catch(t){C(e,!0)}finally{s===v&&t.removeAttribute("id")}}}return ye(e.replace(N,"$1"),t,n,r)}function ee(){var e=[];return function n(r,i){return e.push(r+" ")>t.cacheLength&&delete n[e.shift()],n[r+" "]=i}}function te(e){return e[v]=!0,e}function ne(e){var t=l.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function re(e){return function(t){return k(t,"input")&&t.type===e}}function ie(e){return function(t){return(k(t,"input")||k(t,"button"))&&t.type===e}}function oe(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&J(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function ae(e){return te((function(t){return t=+t,te((function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))}))}))}function se(e){return e&&void 0!==e.getElementsByTagName&&e}function le(e){var n,r=e?e.ownerDocument||e:O;return r!=l&&9===r.nodeType&&r.documentElement?(c=(l=r).documentElement,d=!S.isXMLDoc(l),h=c.matches||c.webkitMatchesSelector||c.msMatchesSelector,c.msMatchesSelector&&O!=l&&(n=l.defaultView)&&n.top!==n&&n.addEventListener("unload",Y),m.getById=ne((function(e){return c.appendChild(e).id=S.expando,!l.getElementsByName||!l.getElementsByName(S.expando).length})),m.disconnectedMatch=ne((function(e){return h.call(e,"*")})),m.scope=ne((function(){return l.querySelectorAll(":scope")})),m.cssHas=ne((function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}})),m.getById?(t.filter.ID=function(e){var t=e.replace(K,Q);return function(e){return e.getAttribute("id")===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&d){var n=t.getElementById(e);return n?[n]:[]}}):(t.filter.ID=function(e){var t=e.replace(K,Q);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&d){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),t.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},t.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&d)return t.getElementsByClassName(e)},p=[],ne((function(e){var t;c.appendChild(e).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||p.push("\\["+q+"*(?:value|"+A+")"),e.querySelectorAll("[id~="+v+"-]").length||p.push("~="),e.querySelectorAll("a#"+v+"+*").length||p.push(".#.+[+~]"),e.querySelectorAll(":checked").length||p.push(":checked"),(t=l.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),c.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),(t=l.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||p.push("\\["+q+"*name"+q+"*="+q+"*(?:''|\"\")")})),m.cssHas||p.push(":has"),p=p.length&&new RegExp(p.join("|")),T=function(e,t){if(e===t)return a=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!m.sortDetached&&t.compareDocumentPosition(e)===n?e===l||e.ownerDocument==O&&Z.contains(O,e)?-1:t===l||t.ownerDocument==O&&Z.contains(O,t)?1:i?u.call(i,e)-u.call(i,t):0:4&n?-1:1)},l):l}for(e in Z.matches=function(e,t){return Z(e,null,null,t)},Z.matchesSelector=function(e,t){if(le(e),d&&!C[t+" "]&&(!p||!p.test(t)))try{var n=h.call(e,t);if(n||m.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){C(t,!0)}return Z(t,l,null,[e]).length>0},Z.contains=function(e,t){return(e.ownerDocument||e)!=l&&le(e),S.contains(e,t)},Z.attr=function(e,n){(e.ownerDocument||e)!=l&&le(e);var r=t.attrHandle[n.toLowerCase()],i=r&&f.call(t.attrHandle,n.toLowerCase())?r(e,n,!d):void 0;return void 0!==i?i:e.getAttribute(n)},Z.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},S.uniqueSort=function(e){var t,n=[],r=0,o=0;if(a=!m.sortStable,i=!m.sortStable&&s.call(e,0),L.call(e,T),a){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)D.call(e,n[r],1)}return i=null,e},S.fn.uniqueSort=function(){return this.pushStack(S.uniqueSort(s.apply(this)))},t=S.expr={cacheLength:50,createPseudo:te,match:z,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(K,Q),e[3]=(e[3]||e[4]||e[5]||"").replace(K,Q),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||Z.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&Z.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return z.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&U.test(n)&&(t=ue(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(K,Q).toLowerCase();return"*"===e?function(){return!0}:function(e){return k(e,t)}},CLASS:function(e){var t=x[e+" "];return t||(t=new RegExp("(^|"+q+")"+e+"("+q+"|$)"))&&x(e,(function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=Z.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(P," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,l){var c,u,d,p,f,h=o!==a?"nextSibling":"previousSibling",g=t.parentNode,m=s&&t.nodeName.toLowerCase(),b=!l&&!s,x=!1;if(g){if(o){for(;h;){for(d=t;d=d[h];)if(s?k(d,m):1===d.nodeType)return!1;f=h="only"===e&&!f&&"nextSibling"}return!0}if(f=[a?g.firstChild:g.lastChild],a&&b){for(x=(p=(c=(u=g[v]||(g[v]={}))[e]||[])[0]===y&&c[1])&&c[2],d=p&&g.childNodes[p];d=++p&&d&&d[h]||(x=p=0)||f.pop();)if(1===d.nodeType&&++x&&d===t){u[e]=[y,p,x];break}}else if(b&&(x=p=(c=(u=t[v]||(t[v]={}))[e]||[])[0]===y&&c[1]),!1===x)for(;(d=++p&&d&&d[h]||(x=p=0)||f.pop())&&(!(s?k(d,m):1===d.nodeType)||!++x||(b&&((u=d[v]||(d[v]={}))[e]=[y,x]),d!==t)););return(x-=i)===r||x%r==0&&x/r>=0}}},PSEUDO:function(e,n){var r,i=t.pseudos[e]||t.setFilters[e.toLowerCase()]||Z.error("unsupported pseudo: "+e);return i[v]?i(n):i.length>1?(r=[e,e,"",n],t.setFilters.hasOwnProperty(e.toLowerCase())?te((function(e,t){for(var r,o=i(e,n),a=o.length;a--;)e[r=u.call(e,o[a])]=!(t[r]=o[a])})):function(e){return i(e,0,r)}):i}},pseudos:{not:te((function(e){var t=[],n=[],r=ve(e.replace(N,"$1"));return r[v]?te((function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))})):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}})),has:te((function(e){return function(t){return Z(e,t).length>0}})),contains:te((function(e){return e=e.replace(K,Q),function(t){return(t.textContent||S.text(t)).indexOf(e)>-1}})),lang:te((function(e){return W.test(e||"")||Z.error("unsupported lang: "+e),e=e.replace(K,Q).toLowerCase(),function(t){var n;do{if(n=d?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(e){var t=r.location&&r.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===c},focus:function(e){return e===function(){try{return l.activeElement}catch(e){}}()&&l.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:oe(!1),disabled:oe(!0),checked:function(e){return k(e,"input")&&!!e.checked||k(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!t.pseudos.empty(e)},header:function(e){return X.test(e.nodeName)},input:function(e){return B.test(e.nodeName)},button:function(e){return k(e,"input")&&"button"===e.type||k(e,"button")},text:function(e){var t;return k(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ae((function(){return[0]})),last:ae((function(e,t){return[t-1]})),eq:ae((function(e,t,n){return[n<0?n+t:n]})),even:ae((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:ae((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:ae((function(e,t,n){var r;for(r=n<0?n+t:n>t?t:n;--r>=0;)e.push(r);return e})),gt:ae((function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e}))}},t.pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=re(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=ie(e);function ce(){}function ue(e,n){var r,i,o,a,s,l,c,u=w[e+" "];if(u)return n?0:u.slice(0);for(s=e,l=[],c=t.preFilter;s;){for(a in r&&!(i=I.exec(s))||(i&&(s=s.slice(i[0].length)||s),l.push(o=[])),r=!1,(i=F.exec(s))&&(r=i.shift(),o.push({value:r,type:i[0].replace(N," ")}),s=s.slice(r.length)),t.filter)!(i=z[a].exec(s))||c[a]&&!(i=c[a](i))||(r=i.shift(),o.push({value:r,type:a,matches:i}),s=s.slice(r.length));if(!r)break}return n?s.length:s?Z.error(e):w(e,l).slice(0)}function de(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function pe(e,t,n){var r=t.dir,i=t.next,o=i||r,a=n&&"parentNode"===o,s=b++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||a)return e(t,n,i);return!1}:function(t,n,l){var c,u,d=[y,s];if(l){for(;t=t[r];)if((1===t.nodeType||a)&&e(t,n,l))return!0}else for(;t=t[r];)if(1===t.nodeType||a)if(u=t[v]||(t[v]={}),i&&k(t,i))t=t[r]||t;else{if((c=u[o])&&c[0]===y&&c[1]===s)return d[2]=c[2];if(u[o]=d,d[2]=e(t,n,l))return!0}return!1}}function fe(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function he(e,t,n,r,i){for(var o,a=[],s=0,l=e.length,c=null!=t;s<l;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),c&&t.push(s)));return a}function ge(e,t,n,r,i,o){return r&&!r[v]&&(r=ge(r)),i&&!i[v]&&(i=ge(i,o)),te((function(o,a,s,l){var c,d,p,f,h=[],m=[],v=a.length,y=o||function(e,t,n){for(var r=0,i=t.length;r<i;r++)Z(e,t[r],n);return n}(t||"*",s.nodeType?[s]:s,[]),b=!e||!o&&t?y:he(y,h,e,s,l);if(n?n(b,f=i||(o?e:v||r)?[]:a,s,l):f=b,r)for(c=he(f,m),r(c,[],s,l),d=c.length;d--;)(p=c[d])&&(f[m[d]]=!(b[m[d]]=p));if(o){if(i||e){if(i){for(c=[],d=f.length;d--;)(p=f[d])&&c.push(b[d]=p);i(null,f=[],c,l)}for(d=f.length;d--;)(p=f[d])&&(c=i?u.call(o,p):h[d])>-1&&(o[c]=!(a[c]=p))}}else f=he(f===a?f.splice(v,f.length):f),i?i(null,a,f,l):g.apply(a,f)}))}function me(e){for(var r,i,o,a=e.length,s=t.relative[e[0].type],l=s||t.relative[" "],c=s?1:0,d=pe((function(e){return e===r}),l,!0),p=pe((function(e){return u.call(r,e)>-1}),l,!0),f=[function(e,t,i){var o=!s&&(i||t!=n)||((r=t).nodeType?d(e,t,i):p(e,t,i));return r=null,o}];c<a;c++)if(i=t.relative[e[c].type])f=[pe(fe(f),i)];else{if((i=t.filter[e[c].type].apply(null,e[c].matches))[v]){for(o=++c;o<a&&!t.relative[e[o].type];o++);return ge(c>1&&fe(f),c>1&&de(e.slice(0,c-1).concat({value:" "===e[c-2].type?"*":""})).replace(N,"$1"),i,c<o&&me(e.slice(c,o)),o<a&&me(e=e.slice(o)),o<a&&de(e))}f.push(i)}return fe(f)}function ve(e,r){var i,o=[],a=[],s=E[e+" "];if(!s){for(r||(r=ue(e)),i=r.length;i--;)(s=me(r[i]))[v]?o.push(s):a.push(s);s=E(e,function(e,r){var i=r.length>0,o=e.length>0,a=function(a,s,c,u,p){var f,h,m,v=0,b="0",x=a&&[],w=[],E=n,C=a||o&&t.find.TAG("*",p),T=y+=null==E?1:Math.random()||.1,A=C.length;for(p&&(n=s==l||s||p);b!==A&&null!=(f=C[b]);b++){if(o&&f){for(h=0,s||f.ownerDocument==l||(le(f),c=!d);m=e[h++];)if(m(f,s||l,c)){g.call(u,f);break}p&&(y=T)}i&&((f=!m&&f)&&v--,a&&x.push(f))}if(v+=b,i&&b!==v){for(h=0;m=r[h++];)m(x,w,s,c);if(a){if(v>0)for(;b--;)x[b]||w[b]||(w[b]=j.call(u));w=he(w)}g.apply(u,w),p&&!a&&w.length>0&&v+r.length>1&&S.uniqueSort(u)}return p&&(y=T,n=E),x};return i?te(a):a}(a,o)),s.selector=e}return s}function ye(e,n,r,i){var o,a,s,l,c,u="function"==typeof e&&e,p=!i&&ue(e=u.selector||e);if(r=r||[],1===p.length){if((a=p[0]=p[0].slice(0)).length>2&&"ID"===(s=a[0]).type&&9===n.nodeType&&d&&t.relative[a[1].type]){if(!(n=(t.find.ID(s.matches[0].replace(K,Q),n)||[])[0]))return r;u&&(n=n.parentNode),e=e.slice(a.shift().value.length)}for(o=z.needsContext.test(e)?0:a.length;o--&&(s=a[o],!t.relative[l=s.type]);)if((c=t.find[l])&&(i=c(s.matches[0].replace(K,Q),V.test(a[0].type)&&se(n.parentNode)||n))){if(a.splice(o,1),!(e=i.length&&de(a)))return g.apply(r,i),r;break}}return(u||ve(e,p))(i,n,!d,r,!n||V.test(e)&&se(n.parentNode)||n),r}ce.prototype=t.filters=t.pseudos,t.setFilters=new ce,m.sortStable=v.split("").sort(T).join("")===v,le(),m.sortDetached=ne((function(e){return 1&e.compareDocumentPosition(l.createElement("fieldset"))})),S.find=Z,S.expr[":"]=S.expr.pseudos,S.unique=S.uniqueSort,Z.compile=ve,Z.select=ye,Z.setDocument=le,Z.tokenize=ue,Z.escape=S.escapeSelector,Z.getText=S.text,Z.isXML=S.isXMLDoc,Z.selectors=S.expr,Z.support=S.support,Z.uniqueSort=S.uniqueSort}();var R=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&S(e).is(n))break;r.push(e)}return r},P=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},I=S.expr.match.needsContext,F=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function $(e,t,n){return v(t)?S.grep(e,(function(e,r){return!!t.call(e,r,e)!==n})):t.nodeType?S.grep(e,(function(e){return e===t!==n})):"string"!=typeof t?S.grep(e,(function(e){return u.call(t,e)>-1!==n})):S.filter(t,e,n)}S.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?S.find.matchesSelector(r,e)?[r]:[]:S.find.matches(e,S.grep(t,(function(e){return 1===e.nodeType})))},S.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(S(e).filter((function(){for(t=0;t<r;t++)if(S.contains(i[t],this))return!0})));for(n=this.pushStack([]),t=0;t<r;t++)S.find(e,i[t],n);return r>1?S.uniqueSort(n):n},filter:function(e){return this.pushStack($(this,e||[],!1))},not:function(e){return this.pushStack($(this,e||[],!0))},is:function(e){return!!$(this,"string"==typeof e&&I.test(e)?S(e):e||[],!1).length}});var U,W=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(S.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||U,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:W.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof S?t[0]:t,S.merge(this,S.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),F.test(r[1])&&S.isPlainObject(t))for(r in t)v(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=b.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(S):S.makeArray(e,this)}).prototype=S.fn,U=S(b);var z=/^(?:parents|prev(?:Until|All))/,B={children:!0,contents:!0,next:!0,prev:!0};function X(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}S.fn.extend({has:function(e){var t=S(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(S.contains(this,t[e]))return!0}))},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&S(e);if(!I.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&S.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?S.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?u.call(S(e),this[0]):u.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(S.uniqueSort(S.merge(this.get(),S(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),S.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return R(e,"parentNode")},parentsUntil:function(e,t,n){return R(e,"parentNode",n)},next:function(e){return X(e,"nextSibling")},prev:function(e){return X(e,"previousSibling")},nextAll:function(e){return R(e,"nextSibling")},prevAll:function(e){return R(e,"previousSibling")},nextUntil:function(e,t,n){return R(e,"nextSibling",n)},prevUntil:function(e,t,n){return R(e,"previousSibling",n)},siblings:function(e){return P((e.parentNode||{}).firstChild,e)},children:function(e){return P(e.firstChild)},contents:function(e){return null!=e.contentDocument&&a(e.contentDocument)?e.contentDocument:(k(e,"template")&&(e=e.content||e),S.merge([],e.childNodes))}},(function(e,t){S.fn[e]=function(n,r){var i=S.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=S.filter(r,i)),this.length>1&&(B[e]||S.uniqueSort(i),z.test(e)&&i.reverse()),this.pushStack(i)}}));var G=/[^\x20\t\r\n\f]+/g;function V(e){return e}function K(e){throw e}function Q(e,t,n,r){var i;try{e&&v(i=e.promise)?i.call(e).done(t).fail(n):e&&v(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}S.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return S.each(e.match(G)||[],(function(e,n){t[n]=!0})),t}(e):S.extend({},e);var t,n,r,i,o=[],a=[],s=-1,l=function(){for(i=i||e.once,r=t=!0;a.length;s=-1)for(n=a.shift();++s<o.length;)!1===o[s].apply(n[0],n[1])&&e.stopOnFalse&&(s=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},c={add:function(){return o&&(n&&!t&&(s=o.length-1,a.push(n)),function t(n){S.each(n,(function(n,r){v(r)?e.unique&&c.has(r)||o.push(r):r&&r.length&&"string"!==E(r)&&t(r)}))}(arguments),n&&!t&&l()),this},remove:function(){return S.each(arguments,(function(e,t){for(var n;(n=S.inArray(t,o,n))>-1;)o.splice(n,1),n<=s&&s--})),this},has:function(e){return e?S.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=a=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=[e,(n=n||[]).slice?n.slice():n],a.push(n),t||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},S.extend({Deferred:function(e){var t=[["notify","progress",S.Callbacks("memory"),S.Callbacks("memory"),2],["resolve","done",S.Callbacks("once memory"),S.Callbacks("once memory"),0,"resolved"],["reject","fail",S.Callbacks("once memory"),S.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return S.Deferred((function(n){S.each(t,(function(t,r){var i=v(e[r[4]])&&e[r[4]];o[r[1]]((function(){var e=i&&i.apply(this,arguments);e&&v(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[e]:arguments)}))})),e=null})).promise()},then:function(e,n,i){var o=0;function a(e,t,n,i){return function(){var s=this,l=arguments,c=function(){var r,c;if(!(e<o)){if((r=n.apply(s,l))===t.promise())throw new TypeError("Thenable self-resolution");c=r&&("object"==typeof r||"function"==typeof r)&&r.then,v(c)?i?c.call(r,a(o,t,V,i),a(o,t,K,i)):(o++,c.call(r,a(o,t,V,i),a(o,t,K,i),a(o,t,V,t.notifyWith))):(n!==V&&(s=void 0,l=[r]),(i||t.resolveWith)(s,l))}},u=i?c:function(){try{c()}catch(r){S.Deferred.exceptionHook&&S.Deferred.exceptionHook(r,u.error),e+1>=o&&(n!==K&&(s=void 0,l=[r]),t.rejectWith(s,l))}};e?u():(S.Deferred.getErrorHook?u.error=S.Deferred.getErrorHook():S.Deferred.getStackHook&&(u.error=S.Deferred.getStackHook()),r.setTimeout(u))}}return S.Deferred((function(r){t[0][3].add(a(0,r,v(i)?i:V,r.notifyWith)),t[1][3].add(a(0,r,v(e)?e:V)),t[2][3].add(a(0,r,v(n)?n:K))})).promise()},promise:function(e){return null!=e?S.extend(e,i):i}},o={};return S.each(t,(function(e,r){var a=r[2],s=r[5];i[r[1]]=a.add,s&&a.add((function(){n=s}),t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),a.add(r[3].fire),o[r[0]]=function(){return o[r[0]+"With"](this===o?void 0:this,arguments),this},o[r[0]+"With"]=a.fireWith})),i.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=s.call(arguments),o=S.Deferred(),a=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?s.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(Q(e,o.done(a(n)).resolve,o.reject,!t),"pending"===o.state()||v(i[n]&&i[n].then)))return o.then();for(;n--;)Q(i[n],a(n),o.reject);return o.promise()}});var Y=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;S.Deferred.exceptionHook=function(e,t){r.console&&r.console.warn&&e&&Y.test(e.name)&&r.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},S.readyException=function(e){r.setTimeout((function(){throw e}))};var J=S.Deferred();function Z(){b.removeEventListener("DOMContentLoaded",Z),r.removeEventListener("load",Z),S.ready()}S.fn.ready=function(e){return J.then(e).catch((function(e){S.readyException(e)})),this},S.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--S.readyWait:S.isReady)||(S.isReady=!0,!0!==e&&--S.readyWait>0||J.resolveWith(b,[S]))}}),S.ready.then=J.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?r.setTimeout(S.ready):(b.addEventListener("DOMContentLoaded",Z),r.addEventListener("load",Z));var ee=function(e,t,n,r,i,o,a){var s=0,l=e.length,c=null==n;if("object"===E(n))for(s in i=!0,n)ee(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,v(r)||(a=!0),c&&(a?(t.call(e,r),t=null):(c=t,t=function(e,t,n){return c.call(S(e),n)})),t))for(;s<l;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:c?t.call(e):l?t(e[0],n):o},te=/^-ms-/,ne=/-([a-z])/g;function re(e,t){return t.toUpperCase()}function ie(e){return e.replace(te,"ms-").replace(ne,re)}var oe=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function ae(){this.expando=S.expando+ae.uid++}ae.uid=1,ae.prototype={cache:function(e){var t=e[this.expando];return t||(t={},oe(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[ie(t)]=n;else for(r in t)i[ie(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][ie(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(ie):(t=ie(t))in r?[t]:t.match(G)||[]).length;for(;n--;)delete r[t[n]]}(void 0===t||S.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!S.isEmptyObject(t)}};var se=new ae,le=new ae,ce=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ue=/[A-Z]/g;function de(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(ue,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:ce.test(e)?JSON.parse(e):e)}(n)}catch(e){}le.set(e,t,n)}else n=void 0;return n}S.extend({hasData:function(e){return le.hasData(e)||se.hasData(e)},data:function(e,t,n){return le.access(e,t,n)},removeData:function(e,t){le.remove(e,t)},_data:function(e,t,n){return se.access(e,t,n)},_removeData:function(e,t){se.remove(e,t)}}),S.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=le.get(o),1===o.nodeType&&!se.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&0===(r=a[n].name).indexOf("data-")&&(r=ie(r.slice(5)),de(o,r,i[r]));se.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each((function(){le.set(this,e)})):ee(this,(function(t){var n;if(o&&void 0===t)return void 0!==(n=le.get(o,e))||void 0!==(n=de(o,e))?n:void 0;this.each((function(){le.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){le.remove(this,e)}))}}),S.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=se.get(e,t),n&&(!r||Array.isArray(n)?r=se.access(e,t,S.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=S.queue(e,t),r=n.length,i=n.shift(),o=S._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,(function(){S.dequeue(e,t)}),o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return se.get(e,n)||se.access(e,n,{empty:S.Callbacks("once memory").add((function(){se.remove(e,[t+"queue",n])}))})}}),S.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?S.queue(this[0],e):void 0===t?this:this.each((function(){var n=S.queue(this,e,t);S._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&S.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){S.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=S.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=se.get(o[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var pe=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,fe=new RegExp("^(?:([+-])=|)("+pe+")([a-z%]*)$","i"),he=["Top","Right","Bottom","Left"],ge=b.documentElement,me=function(e){return S.contains(e.ownerDocument,e)},ve={composed:!0};ge.getRootNode&&(me=function(e){return S.contains(e.ownerDocument,e)||e.getRootNode(ve)===e.ownerDocument});var ye=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&me(e)&&"none"===S.css(e,"display")};function be(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return S.css(e,t,"")},l=s(),c=n&&n[3]||(S.cssNumber[t]?"":"px"),u=e.nodeType&&(S.cssNumber[t]||"px"!==c&&+l)&&fe.exec(S.css(e,t));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;a--;)S.style(e,t,u+c),(1-o)*(1-(o=s()/l||.5))<=0&&(a=0),u/=o;u*=2,S.style(e,t,u+c),n=n||[]}return n&&(u=+u||+l||0,i=n[1]?u+(n[1]+1)*n[2]:+n[2],r&&(r.unit=c,r.start=u,r.end=i)),i}var xe={};function we(e){var t,n=e.ownerDocument,r=e.nodeName,i=xe[r];return i||(t=n.body.appendChild(n.createElement(r)),i=S.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),xe[r]=i,i)}function Ee(e,t){for(var n,r,i=[],o=0,a=e.length;o<a;o++)(r=e[o]).style&&(n=r.style.display,t?("none"===n&&(i[o]=se.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&ye(r)&&(i[o]=we(r))):"none"!==n&&(i[o]="none",se.set(r,"display",n)));for(o=0;o<a;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}S.fn.extend({show:function(){return Ee(this,!0)},hide:function(){return Ee(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){ye(this)?S(this).show():S(this).hide()}))}});var Ce,Te,Se=/^(?:checkbox|radio)$/i,Ae=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ke=/^$|^module$|\/(?:java|ecma)script/i;Ce=b.createDocumentFragment().appendChild(b.createElement("div")),(Te=b.createElement("input")).setAttribute("type","radio"),Te.setAttribute("checked","checked"),Te.setAttribute("name","t"),Ce.appendChild(Te),m.checkClone=Ce.cloneNode(!0).cloneNode(!0).lastChild.checked,Ce.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!Ce.cloneNode(!0).lastChild.defaultValue,Ce.innerHTML="<option></option>",m.option=!!Ce.lastChild;var je={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Le(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&k(e,t)?S.merge([e],n):n}function De(e,t){for(var n=0,r=e.length;n<r;n++)se.set(e[n],"globalEval",!t||se.get(t[n],"globalEval"))}je.tbody=je.tfoot=je.colgroup=je.caption=je.thead,je.th=je.td,m.option||(je.optgroup=je.option=[1,"<select multiple='multiple'>","</select>"]);var qe=/<|&#?\w+;/;function Ne(e,t,n,r,i){for(var o,a,s,l,c,u,d=t.createDocumentFragment(),p=[],f=0,h=e.length;f<h;f++)if((o=e[f])||0===o)if("object"===E(o))S.merge(p,o.nodeType?[o]:o);else if(qe.test(o)){for(a=a||d.appendChild(t.createElement("div")),s=(Ae.exec(o)||["",""])[1].toLowerCase(),l=je[s]||je._default,a.innerHTML=l[1]+S.htmlPrefilter(o)+l[2],u=l[0];u--;)a=a.lastChild;S.merge(p,a.childNodes),(a=d.firstChild).textContent=""}else p.push(t.createTextNode(o));for(d.textContent="",f=0;o=p[f++];)if(r&&S.inArray(o,r)>-1)i&&i.push(o);else if(c=me(o),a=Le(d.appendChild(o),"script"),c&&De(a),n)for(u=0;o=a[u++];)ke.test(o.type||"")&&n.push(o);return d}var He=/^([^.]*)(?:\.(.+)|)/;function _e(){return!0}function Oe(){return!1}function Me(e,t,n,r,i,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Me(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Oe;else if(!i)return e;return 1===o&&(a=i,i=function(e){return S().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=S.guid++)),e.each((function(){S.event.add(this,t,i,r,n)}))}function Re(e,t,n){n?(se.set(e,t,!1),S.event.add(e,t,{namespace:!1,handler:function(e){var n,r=se.get(this,t);if(1&e.isTrigger&&this[t]){if(r)(S.event.special[t]||{}).delegateType&&e.stopPropagation();else if(r=s.call(arguments),se.set(this,t,r),this[t](),n=se.get(this,t),se.set(this,t,!1),r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else r&&(se.set(this,t,S.event.trigger(r[0],r.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=_e)}})):void 0===se.get(e,t)&&S.event.add(e,t,_e)}S.event={global:{},add:function(e,t,n,r,i){var o,a,s,l,c,u,d,p,f,h,g,m=se.get(e);if(oe(e))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&S.find.matchesSelector(ge,i),n.guid||(n.guid=S.guid++),(l=m.events)||(l=m.events=Object.create(null)),(a=m.handle)||(a=m.handle=function(t){return void 0!==S&&S.event.triggered!==t.type?S.event.dispatch.apply(e,arguments):void 0}),c=(t=(t||"").match(G)||[""]).length;c--;)f=g=(s=He.exec(t[c])||[])[1],h=(s[2]||"").split(".").sort(),f&&(d=S.event.special[f]||{},f=(i?d.delegateType:d.bindType)||f,d=S.event.special[f]||{},u=S.extend({type:f,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&S.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=l[f])||((p=l[f]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(e,r,h,a)||e.addEventListener&&e.addEventListener(f,a)),d.add&&(d.add.call(e,u),u.handler.guid||(u.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,u):p.push(u),S.event.global[f]=!0)},remove:function(e,t,n,r,i){var o,a,s,l,c,u,d,p,f,h,g,m=se.hasData(e)&&se.get(e);if(m&&(l=m.events)){for(c=(t=(t||"").match(G)||[""]).length;c--;)if(f=g=(s=He.exec(t[c])||[])[1],h=(s[2]||"").split(".").sort(),f){for(d=S.event.special[f]||{},p=l[f=(r?d.delegateType:d.bindType)||f]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;o--;)u=p[o],!i&&g!==u.origType||n&&n.guid!==u.guid||s&&!s.test(u.namespace)||r&&r!==u.selector&&("**"!==r||!u.selector)||(p.splice(o,1),u.selector&&p.delegateCount--,d.remove&&d.remove.call(e,u));a&&!p.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||S.removeEvent(e,f,m.handle),delete l[f])}else for(f in l)S.event.remove(e,f+t[c],n,r,!0);S.isEmptyObject(l)&&se.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=new Array(arguments.length),l=S.event.fix(e),c=(se.get(this,"events")||Object.create(null))[l.type]||[],u=S.event.special[l.type]||{};for(s[0]=l,t=1;t<arguments.length;t++)s[t]=arguments[t];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(a=S.event.handlers.call(this,l,c),t=0;(i=a[t++])&&!l.isPropagationStopped();)for(l.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(r=((S.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(l.result=r)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,r,i,o,a,s=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&e.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(o=[],a={},n=0;n<l;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?S(i,this).index(c)>-1:S.find(i,this,null,[c]).length),a[i]&&o.push(r);o.length&&s.push({elem:c,handlers:o})}return c=this,l<t.length&&s.push({elem:c,handlers:t.slice(l)}),s},addProp:function(e,t){Object.defineProperty(S.Event.prototype,e,{enumerable:!0,configurable:!0,get:v(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[S.expando]?e:new S.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Se.test(t.type)&&t.click&&k(t,"input")&&Re(t,"click",!0),!1},trigger:function(e){var t=this||e;return Se.test(t.type)&&t.click&&k(t,"input")&&Re(t,"click"),!0},_default:function(e){var t=e.target;return Se.test(t.type)&&t.click&&k(t,"input")&&se.get(t,"click")||k(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},S.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},S.Event=function(e,t){if(!(this instanceof S.Event))return new S.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?_e:Oe,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&S.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[S.expando]=!0},S.Event.prototype={constructor:S.Event,isDefaultPrevented:Oe,isPropagationStopped:Oe,isImmediatePropagationStopped:Oe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=_e,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=_e,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=_e,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},S.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},S.event.addProp),S.each({focus:"focusin",blur:"focusout"},(function(e,t){function n(e){if(b.documentMode){var n=se.get(this,"handle"),r=S.event.fix(e);r.type="focusin"===e.type?"focus":"blur",r.isSimulated=!0,n(e),r.target===r.currentTarget&&n(r)}else S.event.simulate(t,e.target,S.event.fix(e))}S.event.special[e]={setup:function(){var r;if(Re(this,e,!0),!b.documentMode)return!1;(r=se.get(this,t))||this.addEventListener(t,n),se.set(this,t,(r||0)+1)},trigger:function(){return Re(this,e),!0},teardown:function(){var e;if(!b.documentMode)return!1;(e=se.get(this,t)-1)?se.set(this,t,e):(this.removeEventListener(t,n),se.remove(this,t))},_default:function(t){return se.get(t.target,e)},delegateType:t},S.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=se.get(i,t);o||(b.documentMode?this.addEventListener(t,n):r.addEventListener(e,n,!0)),se.set(i,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=se.get(i,t)-1;o?se.set(i,t,o):(b.documentMode?this.removeEventListener(t,n):r.removeEventListener(e,n,!0),se.remove(i,t))}}})),S.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){S.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=e.relatedTarget,i=e.handleObj;return r&&(r===this||S.contains(this,r))||(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}})),S.fn.extend({on:function(e,t,n,r){return Me(this,e,t,n,r)},one:function(e,t,n,r){return Me(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,S(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Oe),this.each((function(){S.event.remove(this,e,n,t)}))}});var Pe=/<script|<style|<link/i,Ie=/checked\s*(?:[^=]|=\s*.checked.)/i,Fe=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function $e(e,t){return k(e,"table")&&k(11!==t.nodeType?t:t.firstChild,"tr")&&S(e).children("tbody")[0]||e}function Ue(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function We(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function ze(e,t){var n,r,i,o,a,s;if(1===t.nodeType){if(se.hasData(e)&&(s=se.get(e).events))for(i in se.remove(t,"handle events"),s)for(n=0,r=s[i].length;n<r;n++)S.event.add(t,i,s[i][n]);le.hasData(e)&&(o=le.access(e),a=S.extend({},o),le.set(t,a))}}function Be(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Se.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Xe(e,t,n,r){t=l(t);var i,o,a,s,c,u,d=0,p=e.length,f=p-1,h=t[0],g=v(h);if(g||p>1&&"string"==typeof h&&!m.checkClone&&Ie.test(h))return e.each((function(i){var o=e.eq(i);g&&(t[0]=h.call(this,i,o.html())),Xe(o,t,n,r)}));if(p&&(o=(i=Ne(t,e[0].ownerDocument,!1,e,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(s=(a=S.map(Le(i,"script"),Ue)).length;d<p;d++)c=i,d!==f&&(c=S.clone(c,!0,!0),s&&S.merge(a,Le(c,"script"))),n.call(e[d],c,d);if(s)for(u=a[a.length-1].ownerDocument,S.map(a,We),d=0;d<s;d++)c=a[d],ke.test(c.type||"")&&!se.access(c,"globalEval")&&S.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?S._evalUrl&&!c.noModule&&S._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):w(c.textContent.replace(Fe,""),c,u))}return e}function Ge(e,t,n){for(var r,i=t?S.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||S.cleanData(Le(r)),r.parentNode&&(n&&me(r)&&De(Le(r,"script")),r.parentNode.removeChild(r));return e}S.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s=e.cloneNode(!0),l=me(e);if(!(m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||S.isXMLDoc(e)))for(a=Le(s),r=0,i=(o=Le(e)).length;r<i;r++)Be(o[r],a[r]);if(t)if(n)for(o=o||Le(e),a=a||Le(s),r=0,i=o.length;r<i;r++)ze(o[r],a[r]);else ze(e,s);return(a=Le(s,"script")).length>0&&De(a,!l&&Le(e,"script")),s},cleanData:function(e){for(var t,n,r,i=S.event.special,o=0;void 0!==(n=e[o]);o++)if(oe(n)){if(t=n[se.expando]){if(t.events)for(r in t.events)i[r]?S.event.remove(n,r):S.removeEvent(n,r,t.handle);n[se.expando]=void 0}n[le.expando]&&(n[le.expando]=void 0)}}}),S.fn.extend({detach:function(e){return Ge(this,e,!0)},remove:function(e){return Ge(this,e)},text:function(e){return ee(this,(function(e){return void 0===e?S.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return Xe(this,arguments,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||$e(this,e).appendChild(e)}))},prepend:function(){return Xe(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=$e(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return Xe(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return Xe(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(S.cleanData(Le(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return S.clone(this,e,t)}))},html:function(e){return ee(this,(function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Pe.test(e)&&!je[(Ae.exec(e)||["",""])[1].toLowerCase()]){e=S.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(S.cleanData(Le(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return Xe(this,arguments,(function(t){var n=this.parentNode;S.inArray(this,e)<0&&(S.cleanData(Le(this)),n&&n.replaceChild(t,this))}),e)}}),S.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){S.fn[e]=function(e){for(var n,r=[],i=S(e),o=i.length-1,a=0;a<=o;a++)n=a===o?this:this.clone(!0),S(i[a])[t](n),c.apply(r,n.get());return this.pushStack(r)}}));var Ve=new RegExp("^("+pe+")(?!px)[a-z%]+$","i"),Ke=/^--/,Qe=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=r),t.getComputedStyle(e)},Ye=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},Je=new RegExp(he.join("|"),"i");function Ze(e,t,n){var r,i,o,a,s=Ke.test(t),l=e.style;return(n=n||Qe(e))&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(N,"$1")||void 0),""!==a||me(e)||(a=S.style(e,t)),!m.pixelBoxStyles()&&Ve.test(a)&&Je.test(t)&&(r=l.width,i=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=a,a=n.width,l.width=r,l.minWidth=i,l.maxWidth=o)),void 0!==a?a+"":a}function et(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ge.appendChild(c).appendChild(u);var e=r.getComputedStyle(u);n="1%"!==e.top,l=12===t(e.marginLeft),u.style.right="60%",a=36===t(e.right),i=36===t(e.width),u.style.position="absolute",o=12===t(u.offsetWidth/3),ge.removeChild(c),u=null}}function t(e){return Math.round(parseFloat(e))}var n,i,o,a,s,l,c=b.createElement("div"),u=b.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===u.style.backgroundClip,S.extend(m,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),a},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,t,n,i;return null==s&&(e=b.createElement("table"),t=b.createElement("tr"),n=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",ge.appendChild(e).appendChild(t).appendChild(n),i=r.getComputedStyle(t),s=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===t.offsetHeight,ge.removeChild(e)),s}}))}();var tt=["Webkit","Moz","ms"],nt=b.createElement("div").style,rt={};function it(e){return S.cssProps[e]||rt[e]||(e in nt?e:rt[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=tt.length;n--;)if((e=tt[n]+t)in nt)return e}(e)||e)}var ot=/^(none|table(?!-c[ea]).+)/,at={position:"absolute",visibility:"hidden",display:"block"},st={letterSpacing:"0",fontWeight:"400"};function lt(e,t,n){var r=fe.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ct(e,t,n,r,i,o){var a="width"===t?1:0,s=0,l=0,c=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(c+=S.css(e,n+he[a],!0,i)),r?("content"===n&&(l-=S.css(e,"padding"+he[a],!0,i)),"margin"!==n&&(l-=S.css(e,"border"+he[a]+"Width",!0,i))):(l+=S.css(e,"padding"+he[a],!0,i),"padding"!==n?l+=S.css(e,"border"+he[a]+"Width",!0,i):s+=S.css(e,"border"+he[a]+"Width",!0,i));return!r&&o>=0&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-s-.5))||0),l+c}function ut(e,t,n){var r=Qe(e),i=(!m.boxSizingReliable()||n)&&"border-box"===S.css(e,"boxSizing",!1,r),o=i,a=Ze(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Ve.test(a)){if(!n)return a;a="auto"}return(!m.boxSizingReliable()&&i||!m.reliableTrDimensions()&&k(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===S.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===S.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+ct(e,t,n||(i?"border":"content"),o,r,a)+"px"}function dt(e,t,n,r,i){return new dt.prototype.init(e,t,n,r,i)}S.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ze(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=ie(t),l=Ke.test(t),c=e.style;if(l||(t=it(s)),a=S.cssHooks[t]||S.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:c[t];"string"==(o=typeof n)&&(i=fe.exec(n))&&i[1]&&(n=be(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=i&&i[3]||(S.cssNumber[s]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,r){var i,o,a,s=ie(t);return Ke.test(t)||(t=it(s)),(a=S.cssHooks[t]||S.cssHooks[s])&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=Ze(e,t,r)),"normal"===i&&t in st&&(i=st[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),S.each(["height","width"],(function(e,t){S.cssHooks[t]={get:function(e,n,r){if(n)return!ot.test(S.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ut(e,t,r):Ye(e,at,(function(){return ut(e,t,r)}))},set:function(e,n,r){var i,o=Qe(e),a=!m.scrollboxSize()&&"absolute"===o.position,s=(a||r)&&"border-box"===S.css(e,"boxSizing",!1,o),l=r?ct(e,t,r,s,o):0;return s&&a&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-ct(e,t,"border",!1,o)-.5)),l&&(i=fe.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=S.css(e,t)),lt(0,n,l)}}})),S.cssHooks.marginLeft=et(m.reliableMarginLeft,(function(e,t){if(t)return(parseFloat(Ze(e,"marginLeft"))||e.getBoundingClientRect().left-Ye(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),S.each({margin:"",padding:"",border:"Width"},(function(e,t){S.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+he[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(S.cssHooks[e+t].set=lt)})),S.fn.extend({css:function(e,t){return ee(this,(function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Qe(e),i=t.length;a<i;a++)o[t[a]]=S.css(e,t[a],!1,r);return o}return void 0!==n?S.style(e,t,n):S.css(e,t)}),e,t,arguments.length>1)}}),S.Tween=dt,dt.prototype={constructor:dt,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||S.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(S.cssNumber[n]?"":"px")},cur:function(){var e=dt.propHooks[this.prop];return e&&e.get?e.get(this):dt.propHooks._default.get(this)},run:function(e){var t,n=dt.propHooks[this.prop];return this.options.duration?this.pos=t=S.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):dt.propHooks._default.set(this),this}},dt.prototype.init.prototype=dt.prototype,dt.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=S.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){S.fx.step[e.prop]?S.fx.step[e.prop](e):1!==e.elem.nodeType||!S.cssHooks[e.prop]&&null==e.elem.style[it(e.prop)]?e.elem[e.prop]=e.now:S.style(e.elem,e.prop,e.now+e.unit)}}},dt.propHooks.scrollTop=dt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},S.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},S.fx=dt.prototype.init,S.fx.step={};var pt,ft,ht=/^(?:toggle|show|hide)$/,gt=/queueHooks$/;function mt(){ft&&(!1===b.hidden&&r.requestAnimationFrame?r.requestAnimationFrame(mt):r.setTimeout(mt,S.fx.interval),S.fx.tick())}function vt(){return r.setTimeout((function(){pt=void 0})),pt=Date.now()}function yt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=he[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function bt(e,t,n){for(var r,i=(xt.tweeners[t]||[]).concat(xt.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function xt(e,t,n){var r,i,o=0,a=xt.prefilters.length,s=S.Deferred().always((function(){delete l.elem})),l=function(){if(i)return!1;for(var t=pt||vt(),n=Math.max(0,c.startTime+c.duration-t),r=1-(n/c.duration||0),o=0,a=c.tweens.length;o<a;o++)c.tweens[o].run(r);return s.notifyWith(e,[c,r,n]),r<1&&a?n:(a||s.notifyWith(e,[c,1,0]),s.resolveWith(e,[c]),!1)},c=s.promise({elem:e,props:S.extend({},t),opts:S.extend(!0,{specialEasing:{},easing:S.easing._default},n),originalProperties:t,originalOptions:n,startTime:pt||vt(),duration:n.duration,tweens:[],createTween:function(t,n){var r=S.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(r),r},stop:function(t){var n=0,r=t?c.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)c.tweens[n].run(1);return t?(s.notifyWith(e,[c,1,0]),s.resolveWith(e,[c,t])):s.rejectWith(e,[c,t]),this}}),u=c.props;for(function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=ie(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=S.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(u,c.opts.specialEasing);o<a;o++)if(r=xt.prefilters[o].call(c,e,u,c.opts))return v(r.stop)&&(S._queueHooks(c.elem,c.opts.queue).stop=r.stop.bind(r)),r;return S.map(u,bt,c),v(c.opts.start)&&c.opts.start.call(e,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),S.fx.timer(S.extend(l,{elem:e,anim:c,queue:c.opts.queue})),c}S.Animation=S.extend(xt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return be(n.elem,e,fe.exec(t),n),n}]},tweener:function(e,t){v(e)?(t=e,e=["*"]):e=e.match(G);for(var n,r=0,i=e.length;r<i;r++)n=e[r],xt.tweeners[n]=xt.tweeners[n]||[],xt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,l,c,u,d="width"in t||"height"in t,p=this,f={},h=e.style,g=e.nodeType&&ye(e),m=se.get(e,"fxshow");for(r in n.queue||(null==(a=S._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always((function(){p.always((function(){a.unqueued--,S.queue(e,"fx").length||a.empty.fire()}))}))),t)if(i=t[r],ht.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;g=!0}f[r]=m&&m[r]||S.style(e,r)}if((l=!S.isEmptyObject(t))||!S.isEmptyObject(f))for(r in d&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(c=m&&m.display)&&(c=se.get(e,"display")),"none"===(u=S.css(e,"display"))&&(c?u=c:(Ee([e],!0),c=e.style.display||c,u=S.css(e,"display"),Ee([e]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===S.css(e,"float")&&(l||(p.done((function(){h.display=c})),null==c&&(u=h.display,c="none"===u?"":u)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always((function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]}))),l=!1,f)l||(m?"hidden"in m&&(g=m.hidden):m=se.access(e,"fxshow",{display:c}),o&&(m.hidden=!g),g&&Ee([e],!0),p.done((function(){for(r in g||Ee([e]),se.remove(e,"fxshow"),f)S.style(e,r,f[r])}))),l=bt(g?m[r]:0,r,p),r in m||(m[r]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?xt.prefilters.unshift(e):xt.prefilters.push(e)}}),S.speed=function(e,t,n){var r=e&&"object"==typeof e?S.extend({},e):{complete:n||!n&&t||v(e)&&e,duration:e,easing:n&&t||t&&!v(t)&&t};return S.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in S.fx.speeds?r.duration=S.fx.speeds[r.duration]:r.duration=S.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){v(r.old)&&r.old.call(this),r.queue&&S.dequeue(this,r.queue)},r},S.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ye).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=S.isEmptyObject(e),o=S.speed(t,n,r),a=function(){var t=xt(this,S.extend({},e),o);(i||se.get(this,"finish"))&&t.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each((function(){var t=!0,i=null!=e&&e+"queueHooks",o=S.timers,a=se.get(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&gt.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||S.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=se.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=S.timers,a=r?r.length:0;for(n.finish=!0,S.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish}))}}),S.each(["toggle","show","hide"],(function(e,t){var n=S.fn[t];S.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(yt(t,!0),e,r,i)}})),S.each({slideDown:yt("show"),slideUp:yt("hide"),slideToggle:yt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){S.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}})),S.timers=[],S.fx.tick=function(){var e,t=0,n=S.timers;for(pt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||S.fx.stop(),pt=void 0},S.fx.timer=function(e){S.timers.push(e),S.fx.start()},S.fx.interval=13,S.fx.start=function(){ft||(ft=!0,mt())},S.fx.stop=function(){ft=null},S.fx.speeds={slow:600,fast:200,_default:400},S.fn.delay=function(e,t){return e=S.fx&&S.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,n){var i=r.setTimeout(t,e);n.stop=function(){r.clearTimeout(i)}}))},function(){var e=b.createElement("input"),t=b.createElement("select").appendChild(b.createElement("option"));e.type="checkbox",m.checkOn=""!==e.value,m.optSelected=t.selected,(e=b.createElement("input")).value="t",e.type="radio",m.radioValue="t"===e.value}();var wt,Et=S.expr.attrHandle;S.fn.extend({attr:function(e,t){return ee(this,S.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){S.removeAttr(this,e)}))}}),S.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?S.prop(e,t,n):(1===o&&S.isXMLDoc(e)||(i=S.attrHooks[t.toLowerCase()]||(S.expr.match.bool.test(t)?wt:void 0)),void 0!==n?null===n?void S.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=S.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!m.radioValue&&"radio"===t&&k(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(G);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),wt={set:function(e,t,n){return!1===t?S.removeAttr(e,n):e.setAttribute(n,n),n}},S.each(S.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=Et[t]||S.find.attr;Et[t]=function(e,t,r){var i,o,a=t.toLowerCase();return r||(o=Et[a],Et[a]=i,i=null!=n(e,t,r)?a:null,Et[a]=o),i}}));var Ct=/^(?:input|select|textarea|button)$/i,Tt=/^(?:a|area)$/i;function St(e){return(e.match(G)||[]).join(" ")}function At(e){return e.getAttribute&&e.getAttribute("class")||""}function kt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(G)||[]}S.fn.extend({prop:function(e,t){return ee(this,S.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[S.propFix[e]||e]}))}}),S.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&S.isXMLDoc(e)||(t=S.propFix[t]||t,i=S.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=S.find.attr(e,"tabindex");return t?parseInt(t,10):Ct.test(e.nodeName)||Tt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(S.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),S.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){S.propFix[this.toLowerCase()]=this})),S.fn.extend({addClass:function(e){var t,n,r,i,o,a;return v(e)?this.each((function(t){S(this).addClass(e.call(this,t,At(this)))})):(t=kt(e)).length?this.each((function(){if(r=At(this),n=1===this.nodeType&&" "+St(r)+" "){for(o=0;o<t.length;o++)i=t[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");a=St(n),r!==a&&this.setAttribute("class",a)}})):this},removeClass:function(e){var t,n,r,i,o,a;return v(e)?this.each((function(t){S(this).removeClass(e.call(this,t,At(this)))})):arguments.length?(t=kt(e)).length?this.each((function(){if(r=At(this),n=1===this.nodeType&&" "+St(r)+" "){for(o=0;o<t.length;o++)for(i=t[o];n.indexOf(" "+i+" ")>-1;)n=n.replace(" "+i+" "," ");a=St(n),r!==a&&this.setAttribute("class",a)}})):this:this.attr("class","")},toggleClass:function(e,t){var n,r,i,o,a=typeof e,s="string"===a||Array.isArray(e);return v(e)?this.each((function(n){S(this).toggleClass(e.call(this,n,At(this),t),t)})):"boolean"==typeof t&&s?t?this.addClass(e):this.removeClass(e):(n=kt(e),this.each((function(){if(s)for(o=S(this),i=0;i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==e&&"boolean"!==a||((r=At(this))&&se.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===e?"":se.get(this,"__className__")||""))})))},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+St(At(n))+" ").indexOf(t)>-1)return!0;return!1}});var jt=/\r/g;S.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=v(e),this.each((function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,S(this).val()):e)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=S.map(i,(function(e){return null==e?"":e+""}))),(t=S.valHooks[this.type]||S.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))}))):i?(t=S.valHooks[i.type]||S.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(jt,""):null==n?"":n:void 0}}),S.extend({valHooks:{option:{get:function(e){var t=S.find.attr(e,"value");return null!=t?t:St(S.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],l=a?o+1:i.length;for(r=o<0?l:a?o:0;r<l;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!k(n.parentNode,"optgroup"))){if(t=S(n).val(),a)return t;s.push(t)}return s},set:function(e,t){for(var n,r,i=e.options,o=S.makeArray(t),a=i.length;a--;)((r=i[a]).selected=S.inArray(S.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),S.each(["radio","checkbox"],(function(){S.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=S.inArray(S(e).val(),t)>-1}},m.checkOn||(S.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var Lt=r.location,Dt={guid:Date.now()},qt=/\?/;S.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new r.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||S.error("Invalid XML: "+(n?S.map(n.childNodes,(function(e){return e.textContent})).join("\n"):e)),t};var Nt=/^(?:focusinfocus|focusoutblur)$/,Ht=function(e){e.stopPropagation()};S.extend(S.event,{trigger:function(e,t,n,i){var o,a,s,l,c,u,d,p,h=[n||b],g=f.call(e,"type")?e.type:e,m=f.call(e,"namespace")?e.namespace.split("."):[];if(a=p=s=n=n||b,3!==n.nodeType&&8!==n.nodeType&&!Nt.test(g+S.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),c=g.indexOf(":")<0&&"on"+g,(e=e[S.expando]?e:new S.Event(g,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:S.makeArray(t,[e]),d=S.event.special[g]||{},i||!d.trigger||!1!==d.trigger.apply(n,t))){if(!i&&!d.noBubble&&!y(n)){for(l=d.delegateType||g,Nt.test(l+g)||(a=a.parentNode);a;a=a.parentNode)h.push(a),s=a;s===(n.ownerDocument||b)&&h.push(s.defaultView||s.parentWindow||r)}for(o=0;(a=h[o++])&&!e.isPropagationStopped();)p=a,e.type=o>1?l:d.bindType||g,(u=(se.get(a,"events")||Object.create(null))[e.type]&&se.get(a,"handle"))&&u.apply(a,t),(u=c&&a[c])&&u.apply&&oe(a)&&(e.result=u.apply(a,t),!1===e.result&&e.preventDefault());return e.type=g,i||e.isDefaultPrevented()||d._default&&!1!==d._default.apply(h.pop(),t)||!oe(n)||c&&v(n[g])&&!y(n)&&((s=n[c])&&(n[c]=null),S.event.triggered=g,e.isPropagationStopped()&&p.addEventListener(g,Ht),n[g](),e.isPropagationStopped()&&p.removeEventListener(g,Ht),S.event.triggered=void 0,s&&(n[c]=s)),e.result}},simulate:function(e,t,n){var r=S.extend(new S.Event,n,{type:e,isSimulated:!0});S.event.trigger(r,null,t)}}),S.fn.extend({trigger:function(e,t){return this.each((function(){S.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return S.event.trigger(e,t,n,!0)}});var _t=/\[\]$/,Ot=/\r?\n/g,Mt=/^(?:submit|button|image|reset|file)$/i,Rt=/^(?:input|select|textarea|keygen)/i;function Pt(e,t,n,r){var i;if(Array.isArray(t))S.each(t,(function(t,i){n||_t.test(e)?r(e,i):Pt(e+"["+("object"==typeof i&&null!=i?t:"")+"]",i,n,r)}));else if(n||"object"!==E(t))r(e,t);else for(i in t)Pt(e+"["+i+"]",t[i],n,r)}S.param=function(e,t){var n,r=[],i=function(e,t){var n=v(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!S.isPlainObject(e))S.each(e,(function(){i(this.name,this.value)}));else for(n in e)Pt(n,e[n],t,i);return r.join("&")},S.fn.extend({serialize:function(){return S.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=S.prop(this,"elements");return e?S.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!S(this).is(":disabled")&&Rt.test(this.nodeName)&&!Mt.test(e)&&(this.checked||!Se.test(e))})).map((function(e,t){var n=S(this).val();return null==n?null:Array.isArray(n)?S.map(n,(function(e){return{name:t.name,value:e.replace(Ot,"\r\n")}})):{name:t.name,value:n.replace(Ot,"\r\n")}})).get()}});var It=/%20/g,Ft=/#.*$/,$t=/([?&])_=[^&]*/,Ut=/^(.*?):[ \t]*([^\r\n]*)$/gm,Wt=/^(?:GET|HEAD)$/,zt=/^\/\//,Bt={},Xt={},Gt="*/".concat("*"),Vt=b.createElement("a");function Kt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(G)||[];if(v(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function Qt(e,t,n,r){var i={},o=e===Xt;function a(s){var l;return i[s]=!0,S.each(e[s]||[],(function(e,s){var c=s(t,n,r);return"string"!=typeof c||o||i[c]?o?!(l=c):void 0:(t.dataTypes.unshift(c),a(c),!1)})),l}return a(t.dataTypes[0])||!i["*"]&&a("*")}function Yt(e,t){var n,r,i=S.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&S.extend(!0,e,r),e}Vt.href=Lt.href,S.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Lt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Lt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Gt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":S.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Yt(Yt(e,S.ajaxSettings),t):Yt(S.ajaxSettings,e)},ajaxPrefilter:Kt(Bt),ajaxTransport:Kt(Xt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,i,o,a,s,l,c,u,d,p,f=S.ajaxSetup({},t),h=f.context||f,g=f.context&&(h.nodeType||h.jquery)?S(h):S.event,m=S.Deferred(),v=S.Callbacks("once memory"),y=f.statusCode||{},x={},w={},E="canceled",C={readyState:0,getResponseHeader:function(e){var t;if(c){if(!a)for(a={};t=Ut.exec(o);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return c?o:null},setRequestHeader:function(e,t){return null==c&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,x[e]=t),this},overrideMimeType:function(e){return null==c&&(f.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)C.always(e[C.status]);else for(t in e)y[t]=[y[t],e[t]];return this},abort:function(e){var t=e||E;return n&&n.abort(t),T(0,t),this}};if(m.promise(C),f.url=((e||f.url||Lt.href)+"").replace(zt,Lt.protocol+"//"),f.type=t.method||t.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(G)||[""],null==f.crossDomain){l=b.createElement("a");try{l.href=f.url,l.href=l.href,f.crossDomain=Vt.protocol+"//"+Vt.host!=l.protocol+"//"+l.host}catch(e){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=S.param(f.data,f.traditional)),Qt(Bt,f,t,C),c)return C;for(d in(u=S.event&&f.global)&&0==S.active++&&S.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Wt.test(f.type),i=f.url.replace(Ft,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(It,"+")):(p=f.url.slice(i.length),f.data&&(f.processData||"string"==typeof f.data)&&(i+=(qt.test(i)?"&":"?")+f.data,delete f.data),!1===f.cache&&(i=i.replace($t,"$1"),p=(qt.test(i)?"&":"?")+"_="+Dt.guid+++p),f.url=i+p),f.ifModified&&(S.lastModified[i]&&C.setRequestHeader("If-Modified-Since",S.lastModified[i]),S.etag[i]&&C.setRequestHeader("If-None-Match",S.etag[i])),(f.data&&f.hasContent&&!1!==f.contentType||t.contentType)&&C.setRequestHeader("Content-Type",f.contentType),C.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Gt+"; q=0.01":""):f.accepts["*"]),f.headers)C.setRequestHeader(d,f.headers[d]);if(f.beforeSend&&(!1===f.beforeSend.call(h,C,f)||c))return C.abort();if(E="abort",v.add(f.complete),C.done(f.success),C.fail(f.error),n=Qt(Xt,f,t,C)){if(C.readyState=1,u&&g.trigger("ajaxSend",[C,f]),c)return C;f.async&&f.timeout>0&&(s=r.setTimeout((function(){C.abort("timeout")}),f.timeout));try{c=!1,n.send(x,T)}catch(e){if(c)throw e;T(-1,e)}}else T(-1,"No Transport");function T(e,t,a,l){var d,p,b,x,w,E=t;c||(c=!0,s&&r.clearTimeout(s),n=void 0,o=l||"",C.readyState=e>0?4:0,d=e>=200&&e<300||304===e,a&&(x=function(e,t,n){for(var r,i,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||e.converters[i+" "+l[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==l[0]&&l.unshift(o),n[o]}(f,C,a)),!d&&S.inArray("script",f.dataTypes)>-1&&S.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),x=function(e,t,n,r){var i,o,a,s,l,c={},u=e.dataTypes.slice();if(u[1])for(a in e.converters)c[a.toLowerCase()]=e.converters[a];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=c[l+" "+o]||c["* "+o]))for(i in c)if((s=i.split(" "))[1]===o&&(a=c[l+" "+s[0]]||c["* "+s[0]])){!0===a?a=c[i]:!0!==c[i]&&(o=s[0],u.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(f,x,C,d),d?(f.ifModified&&((w=C.getResponseHeader("Last-Modified"))&&(S.lastModified[i]=w),(w=C.getResponseHeader("etag"))&&(S.etag[i]=w)),204===e||"HEAD"===f.type?E="nocontent":304===e?E="notmodified":(E=x.state,p=x.data,d=!(b=x.error))):(b=E,!e&&E||(E="error",e<0&&(e=0))),C.status=e,C.statusText=(t||E)+"",d?m.resolveWith(h,[p,E,C]):m.rejectWith(h,[C,E,b]),C.statusCode(y),y=void 0,u&&g.trigger(d?"ajaxSuccess":"ajaxError",[C,f,d?p:b]),v.fireWith(h,[C,E]),u&&(g.trigger("ajaxComplete",[C,f]),--S.active||S.event.trigger("ajaxStop")))}return C},getJSON:function(e,t,n){return S.get(e,t,n,"json")},getScript:function(e,t){return S.get(e,void 0,t,"script")}}),S.each(["get","post"],(function(e,t){S[t]=function(e,n,r,i){return v(n)&&(i=i||r,r=n,n=void 0),S.ajax(S.extend({url:e,type:t,dataType:i,data:n,success:r},S.isPlainObject(e)&&e))}})),S.ajaxPrefilter((function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")})),S._evalUrl=function(e,t,n){return S.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){S.globalEval(e,t,n)}})},S.fn.extend({wrapAll:function(e){var t;return this[0]&&(v(e)&&(e=e.call(this[0])),t=S(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e})).append(this)),this},wrapInner:function(e){return v(e)?this.each((function(t){S(this).wrapInner(e.call(this,t))})):this.each((function(){var t=S(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=v(e);return this.each((function(n){S(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){return this.parent(e).not("body").each((function(){S(this).replaceWith(this.childNodes)})),this}}),S.expr.pseudos.hidden=function(e){return!S.expr.pseudos.visible(e)},S.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},S.ajaxSettings.xhr=function(){try{return new r.XMLHttpRequest}catch(e){}};var Jt={0:200,1223:204},Zt=S.ajaxSettings.xhr();m.cors=!!Zt&&"withCredentials"in Zt,m.ajax=Zt=!!Zt,S.ajaxTransport((function(e){var t,n;if(m.cors||Zt&&!e.crossDomain)return{send:function(i,o){var a,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(a in e.xhrFields)s[a]=e.xhrFields[a];for(a in e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)s.setRequestHeader(a,i[a]);t=function(e){return function(){t&&(t=n=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(Jt[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=t(),n=s.onerror=s.ontimeout=t("error"),void 0!==s.onabort?s.onabort=n:s.onreadystatechange=function(){4===s.readyState&&r.setTimeout((function(){t&&n()}))},t=t("abort");try{s.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}})),S.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),S.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return S.globalEval(e),e}}}),S.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),S.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(r,i){t=S("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),b.head.appendChild(t[0])},abort:function(){n&&n()}}}));var en,tn=[],nn=/(=)\?(?=&|$)|\?\?/;S.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=tn.pop()||S.expando+"_"+Dt.guid++;return this[e]=!0,e}}),S.ajaxPrefilter("json jsonp",(function(e,t,n){var i,o,a,s=!1!==e.jsonp&&(nn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=v(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(nn,"$1"+i):!1!==e.jsonp&&(e.url+=(qt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return a||S.error(i+" was not called"),a[0]},e.dataTypes[0]="json",o=r[i],r[i]=function(){a=arguments},n.always((function(){void 0===o?S(r).removeProp(i):r[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,tn.push(i)),a&&v(o)&&o(a[0]),a=o=void 0})),"script"})),m.createHTMLDocument=((en=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===en.childNodes.length),S.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(m.createHTMLDocument?((r=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(r)):t=b),o=!n&&[],(i=F.exec(e))?[t.createElement(i[1])]:(i=Ne([e],t,o),o&&o.length&&S(o).remove(),S.merge([],i.childNodes)));var r,i,o},S.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return s>-1&&(r=St(e.slice(s)),e=e.slice(0,s)),v(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),a.length>0&&S.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done((function(e){o=arguments,a.html(r?S("<div>").append(S.parseHTML(e)).find(r):e)})).always(n&&function(e,t){a.each((function(){n.apply(this,o||[e.responseText,t,e])}))}),this},S.expr.pseudos.animated=function(e){return S.grep(S.timers,(function(t){return e===t.elem})).length},S.offset={setOffset:function(e,t,n){var r,i,o,a,s,l,c=S.css(e,"position"),u=S(e),d={};"static"===c&&(e.style.position="relative"),s=u.offset(),o=S.css(e,"top"),l=S.css(e,"left"),("absolute"===c||"fixed"===c)&&(o+l).indexOf("auto")>-1?(a=(r=u.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(l)||0),v(t)&&(t=t.call(e,n,S.extend({},s))),null!=t.top&&(d.top=t.top-s.top+a),null!=t.left&&(d.left=t.left-s.left+i),"using"in t?t.using.call(e,d):u.css(d)}},S.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){S.offset.setOffset(this,e,t)}));var t,n,r=this[0];return r?r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===S.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===S.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=S(e).offset()).top+=S.css(e,"borderTopWidth",!0),i.left+=S.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-S.css(r,"marginTop",!0),left:t.left-i.left-S.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent;e&&"static"===S.css(e,"position");)e=e.offsetParent;return e||ge}))}}),S.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;S.fn[e]=function(r){return ee(this,(function(e,r,i){var o;if(y(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i}),e,r,arguments.length)}})),S.each(["top","left"],(function(e,t){S.cssHooks[t]=et(m.pixelPosition,(function(e,n){if(n)return n=Ze(e,t),Ve.test(n)?S(e).position()[t]+"px":n}))})),S.each({Height:"height",Width:"width"},(function(e,t){S.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,r){S.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(!0===i||!0===o?"margin":"border");return ee(this,(function(t,n,i){var o;return y(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?S.css(t,n,s):S.style(t,n,i,s)}),t,a?i:void 0,a)}}))})),S.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){S.fn[t]=function(e){return this.on(t,e)}})),S.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(e,t){S.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}));var rn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;S.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),v(e))return r=s.call(arguments,2),i=function(){return e.apply(t||this,r.concat(s.call(arguments)))},i.guid=e.guid=e.guid||S.guid++,i},S.holdReady=function(e){e?S.readyWait++:S.ready(!0)},S.isArray=Array.isArray,S.parseJSON=JSON.parse,S.nodeName=k,S.isFunction=v,S.isWindow=y,S.camelCase=ie,S.type=E,S.now=Date.now,S.isNumeric=function(e){var t=S.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},S.trim=function(e){return null==e?"":(e+"").replace(rn,"$1")},void 0===(n=function(){return S}.apply(t,[]))||(e.exports=n);var on=r.jQuery,an=r.$;return S.noConflict=function(e){return r.$===S&&(r.$=an),e&&r.jQuery===S&&(r.jQuery=on),S},void 0===i&&(r.jQuery=r.$=S),S}))}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){var e,t,r,i,o=n(289).Ay,a=n(616);if(void 0!==o){var s=function(){return o&&o.VERSION&&o.VERSION>=2},l=function(e){return s()?e.detail:e},c=function(e){return s()?e.detail.request:e.xhr};e=function(e,t){var n=t.init,r=t.success,i=t.before,u=t.complete,d=t.interaction,p=function(e,t){return this.name=t,this.initialize=function(e){n&&e.addEventListener("init",(function(e){n(l(e).defaultOptions)})),r&&e.addEventListener("success",(function(e){var t=s()?e.detail.payload:e.response;r(t,l(e).options)}));var t=e;s()&&(t=t.uiHandler),t.addEventListener("interaction",(function(e){s()?e.detail.options.nette={el:a(e.detail.element)}:e.options.nette={el:a(e.element)},d&&(d(l(e).options)||e.preventDefault())})),i&&e.addEventListener("before",(function(e){i(c(e),l(e).options)||e.preventDefault()})),u&&e.addEventListener("complete",(function(e){u(c(e),l(e).options)}))},s()||this.initialize(e),this};s()?o.registerExtension(new p(null,e)):o.registerExtension(p,e)},t=function(e){var t=e.type||"GET",n=e.data||null;o.makeRequest(t,e.url,n,{}).then(e.success).catch(e.error)},r=function(){o.load()},i=function(e){return o.uiHandler.submitForm(e.get(0))}}else{if(!a.nette)throw new Error("Include Naja.js or nette.ajax for datagrids to work!");e=function(e,t){a.nette.ext(e,t)},t=function(e){a.nette.ajax(e)},r=function(){a.nette.load()},i=function(e){return e.submit()}}var u,d,p,f,h,g,m=[].indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(t in this&&this[t]===e)return t;return-1};a(document).on("click","[data-datagrid-confirm]:not(.ajax)",(function(e){if(!confirm(a(e.target).closest("a").attr("data-datagrid-confirm")))return e.stopPropagation(),e.preventDefault()})),e("datagrid.confirm",void 0!==o?{interaction:function(e){var t;return!e.nette||!(t=e.nette.el.data("datagrid-confirm"))||confirm(t)}}:{before:function(e,t){var n;return!t.nette||!(n=t.nette.el.data("datagrid-confirm"))||confirm(n)}}),a(document).on("change","select[data-autosubmit-per-page]",(function(){var e;return 0===(e=a(this).parent().find("input[type=submit]")).length&&(e=a(this).parent().find("button[type=submit]")),e.click()})).on("change","select[data-autosubmit]",(function(){return i(a(this).closest("form").first())})).on("change","input[data-autosubmit][data-autosubmit-change]",(function(e){var t;return e.which||e.keyCode,clearTimeout(window.datagrid_autosubmit_timer),t=a(this),window.datagrid_autosubmit_timer=setTimeout((function(){return i(t.closest("form").first())}),200)})).on("keyup","input[data-autosubmit]",(function(e){var t,n;if(13===(n=e.which||e.keyCode||0)||!(n>=9&&n<=40||n>=112&&n<=123))return clearTimeout(window.datagrid_autosubmit_timer),t=a(this),window.datagrid_autosubmit_timer=setTimeout((function(){return i(t.closest("form").first())}),200)})).on("keydown",".datagrid-inline-edit input",(function(e){if(13===(e.which||e.keyCode||0))return e.stopPropagation(),e.preventDefault(),a(this).closest("tr").find('.col-action-inline-edit [name="inline_edit[submit]"]').click()})),a(document).on("keydown","input[data-datagrid-manualsubmit]",(function(e){if(13===(e.which||e.keyCode||0))return e.stopPropagation(),e.preventDefault(),i(a(this).closest("form").first())})),h=function(e){var t,n;if(m.call(e,n)>=0)return e.path;for(n=[],t=e.target;t!==document.body&&null!==t;)n.push(t),t=t.parentNode;return n},g=null,document.addEventListener("click",(function(e){var t,n,r,i,o,s,l,c,u,d,p,f,m,v,y,b,x;for(o=0,d=(m=h(e)).length;o<d;o++)if(r=m[o],a(r).is(".col-checkbox")&&g&&e.shiftKey){if(n=a(r).closest("tr"),t=(u=g.closest("tr")).closest("tbody").find("tr").toArray(),n.index()>u.index()?x=t.slice(u.index(),n.index()):n.index()<u.index()&&(x=t.slice(n.index()+1,u.index())),!x)return;for(l=0,p=x.length;l<p;l++)b=x[l],(s=a(b).find(".col-checkbox input[type=checkbox]")[0])&&(s.checked=!0,window.navigator.userAgent.indexOf("MSIE ")?(i=document.createEvent("Event")).initEvent("change",!0,!0):i=new Event("change",{bubbles:!0}),s.dispatchEvent(i))}for(y=[],c=0,f=(v=h(e)).length;c<f;c++)r=v[c],a(r).is(".col-checkbox")?y.push(g=a(r)):y.push(void 0);return y})),document.addEventListener("change",(function(e){var t,n,r,i,o,a,s,l,c,u,d,p;if((o=e.target.getAttribute("data-check"))&&(n=document.querySelectorAll("input[data-check-all-"+o+"]:checked"),d=document.querySelector(".datagrid-"+o+' select[name="group_action[group_action]"]'),t=document.querySelectorAll(".datagrid-"+o+' .row-group-actions *[type="submit"]'),r=document.querySelector(".datagrid-"+o+" .datagrid-selected-rows-count"),n.length?(t&&t.forEach((function(e){e.disabled=!1})),d&&(d.disabled=!1),p=document.querySelectorAll("input[data-check-all-"+o+"]").length,r&&(r.innerHTML=n.length+"/"+p)):(t&&t.forEach((function(e){e.disabled=!0})),d&&(d.disabled=!0,d.value=""),r&&(r.innerHTML="")),window.navigator.userAgent.indexOf("MSIE ")?(i=document.createEvent("Event")).initEvent("change",!0,!0):i=new Event("change",{bubbles:!0}),d&&d.dispatchEvent(i)),o=e.target.getAttribute("data-check-all")){for(u=[],a=0,c=(l=document.querySelectorAll("input[type=checkbox][data-check-all-"+o+"]")).length;a<c;a++)(s=l[a]).checked=e.target.checked,window.navigator.userAgent.indexOf("MSIE ")?(i=document.createEvent("Event")).initEvent("change",!0,!0):i=new Event("change",{bubbles:!0}),u.push(s.dispatchEvent(i));return u}})),window.datagridSerializeUrl=function(e,t){var n=[];for(var r in e)if(e.hasOwnProperty(r)){var i=t?t+"["+r+"]":r,o=e[r];if(null!==o&&""!==o)if("object"==typeof o){var a=window.datagridSerializeUrl(o,i);a&&n.push(a)}else n.push(encodeURIComponent(i)+"="+encodeURIComponent(o))}return n.join("&")},p=function(){if(void 0!==a.fn.sortable)return a(".datagrid [data-sortable]").sortable({handle:".handle-sort",items:"tr",axis:"y",update:function(e,n){var r,i,o,s,l,c,u;return o=(c=n.item.closest("tr[data-id]")).data("id"),l=null,s=null,c.prev().length&&(l=c.prev().data("id")),c.next().length&&(s=c.next().data("id")),u=a(this).data("sortable-url"),(i={})[((r=c.closest(".datagrid").find("tbody").attr("data-sortable-parent-path"))+"-item_id").replace(/^-/,"")]=o,null!==l&&(i[(r+"-prev_id").replace(/^-/,"")]=l),null!==s&&(i[(r+"-next_id").replace(/^-/,"")]=s),t({type:"GET",url:u,data:i,error:function(e,t,n){return alert(e.statusText)}})},helper:function(e,t){return t.children().each((function(){return a(this).width(a(this).width())})),t}})},a((function(){return p()})),void 0===f&&(f=function(){if(void 0!==a(".datagrid-tree-item-children").sortable)return a(".datagrid-tree-item-children").sortable({handle:".handle-sort",items:".datagrid-tree-item:not(.datagrid-tree-header)",toleranceElement:"> .datagrid-tree-item-content",connectWith:".datagrid-tree-item-children",update:function(e,n){var r,i,o,s,l,c,u,d,p;if(a(".toggle-tree-to-delete").remove(),o=(d=n.item.closest(".datagrid-tree-item[data-id]")).data("id"),u=null,s=null,c=null,d.prev().length&&(u=d.prev().data("id")),d.next().length&&(s=d.next().data("id")),(l=d.parent().closest(".datagrid-tree-item")).length&&(l.find(".datagrid-tree-item-children").first().css({display:"block"}),l.addClass("has-children"),c=l.data("id")),p=a(this).data("sortable-url"))return l.find("[data-toggle-tree]").first().removeClass("hidden"),(i={})[((r=d.closest(".datagrid-tree").attr("data-sortable-parent-path"))+"-item_id").replace(/^-/,"")]=o,null!==u&&(i[(r+"-prev_id").replace(/^-/,"")]=u),null!==s&&(i[(r+"-next_id").replace(/^-/,"")]=s),i[(r+"-parent_id").replace(/^-/,"")]=c,t({type:"GET",url:p,data:i,error:function(e,t,n){if("abort"!==n)return alert(e.statusText)}})},stop:function(e,t){return a(".toggle-tree-to-delete").removeClass("toggle-tree-to-delete")},start:function(e,t){var n;if((n=t.item.parent().closest(".datagrid-tree-item")).length&&2===n.find(".datagrid-tree-item").length)return n.find("[data-toggle-tree]").addClass("toggle-tree-to-delete")}})}),a((function(){return f()})),e("datagrid.happy",{success:function(){var e,t,n,r,i,o,s,l,c,u,d;for(window.happy&&window.happy.reset(),d=[],o=0,c=(i=a(".datagrid")).length;o<c;o++){for(t="",l=0,u=(n=i[o].classList).length;l<u;l++)t=t+"."+n[l];1===(e=document.querySelectorAll(t+" input[data-check]:checked")).length&&"toggle-all"===e[0].getAttribute("name")&&(s=document.querySelector(t+" input[name=toggle-all]"))?(s.checked=!1,window.navigator.userAgent.indexOf("MSIE ")?(r=document.createEvent("Event")).initEvent("change",!0,!0):r=new Event("change",{bubbles:!0}),d.push(s.dispatchEvent(r))):d.push(void 0)}return d}}),e("datagrid.sortable",{success:function(){return p()}}),e("datagrid.forms",{success:function(){return a(".datagrid").find("form").each((function(){return window.Nette.initForm(this)}))}}),e("datagrid.url",{success:function(e){var t,n,r,i;if(e._datagrid_url&&window.history.replaceState&&(t=window.location.protocol+"//"+window.location.host,n=window.location.pathname,i=(r=window.datagridSerializeUrl(e.state).replace(/&+$/gm,""))?t+n+"?"+r.replace(/\&*$/,""):t+n,i+=window.location.hash,window.location.href!==i))return window.history.replaceState({path:i},"",i)}}),e("datagrid.sort",{success:function(e){var t,n,r,i;if(e._datagrid_sort){for(n in i=[],r=e._datagrid_sort)t=r[n],i.push(a("#datagrid-sort-"+n).attr("href",t));return i}}}),e("datargid.item_detail",{before:function(e,t){var n,r,i;return!t.nette||!t.nette.el.attr("data-toggle-detail")||(n=t.nette.el.attr("data-toggle-detail"),i=t.nette.el.attr("data-toggle-detail-grid-fullname"),(r=a(".item-detail-"+i+"-id-"+n)).hasClass("loaded")?r.find(".item-detail-content").length?(r.hasClass("toggled")?r.find(".item-detail-content").slideToggle("fast",(function(){return r.toggleClass("toggled")})):(r.toggleClass("toggled"),r.find(".item-detail-content").slideToggle("fast")),!1):(r.removeClass("toggled"),!0):r.addClass("loaded"))},success:function(e){var t,n,r;if(e._datagrid_toggle_detail&&e._datagrid_name)return t=e._datagrid_toggle_detail,r=e._datagrid_name,(n=a(".item-detail-"+r+"-id-"+t)).toggleClass("toggled"),n.find(".item-detail-content").slideToggle("fast")}}),e("datagrid.tree",{before:function(e,t){var n;return!(t.nette&&t.nette.el.attr("data-toggle-tree")&&(t.nette.el.toggleClass("toggle-rotate"),(n=t.nette.el.closest(".datagrid-tree-item").find(".datagrid-tree-item-children").first()).hasClass("loaded"))&&(n.slideToggle("fast"),1))},success:function(e){var t,n,i,o,s,l,c;if(e._datagrid_tree){for(o in i=e._datagrid_tree,(t=a('.datagrid-tree-item[data-id="'+i+'"]').find(".datagrid-tree-item-children").first()).addClass("loaded"),s=e.snippets)l=s[o],n=a(l),(c=a('<div class="datagrid-tree-item" id="'+o+'">')).attr("data-id",n.attr("data-id")),c.append(n),n.data("has-children")&&c.addClass("has-children"),t.append(c);t.addClass("loaded"),t.slideToggle("fast"),r()}return f()}}),a(document).on("click","[data-datagrid-editable-url]",(function(e){var n,r,i,o,s,l,c,u,d,p;if(o=a(this),"a"!==e.target.tagName.toLowerCase()&&!o.hasClass("datagrid-inline-edit")&&!o.hasClass("editing")){for(n in o.addClass("editing"),s=o.html().trim().replace("<br>","\n"),p=o.attr("data-datagrid-editable-value")?o.data("datagrid-editable-value"):s,o.data("originalValue",s),o.data("valueToEdit",p),"textarea"===o.data("datagrid-editable-type")?(u=a("<textarea>"+p+"</textarea>"),c=parseInt(o.css("padding").replace(/[^-\d\.]/g,""),10),l=(o.outerHeight()-2*c)/Math.round(parseFloat(o.css("line-height"))),u.attr("rows",Math.round(l))):"select"===o.data("datagrid-editable-type")?(u=a(o.data("datagrid-editable-element"))).find("option[value='"+p+"']").prop("selected",!0):(u=a('<input type="'+o.data("datagrid-editable-type")+'">')).val(p),i=o.data("datagrid-editable-attrs"))r=i[n],u.attr(n,r);return o.removeClass("edited"),o.html(u),d=function(e,n){var r;return(r=n.val())!==e.data("valueToEdit")?t({url:e.data("datagrid-editable-url"),data:{value:r},type:"POST",success:function(t){return"select"===e.data("datagrid-editable-type")?e.html(u.find("option[value='"+r+"']").html()):(t._datagrid_editable_new_value&&(r=t._datagrid_editable_new_value),e.html(r)),e.addClass("edited")},error:function(){return e.html(e.data("originalValue")),e.addClass("edited-error")}}):e.html(e.data("originalValue")),setTimeout((function(){return e.removeClass("editing")}),1200)},o.find("input,textarea,select").focus().on("blur",(function(){return d(o,a(this))})).on("keydown",(function(e){return"textarea"!==o.data("datagrid-editable-type")&&13===e.which?(e.stopPropagation(),e.preventDefault(),d(o,a(this))):27===e.which?(e.stopPropagation(),e.preventDefault(),o.removeClass("editing"),o.html(o.data("originalValue"))):void 0})),o.find("select").on("change",(function(){return d(o,a(this))}))}})),e("datagrid.after_inline_edit",{success:function(e){var t=a(".datagrid-"+e._datagrid_name);return e._datagrid_inline_edited?(t.find("tr[data-id="+e._datagrid_inline_edited+"] > td").addClass("edited"),t.find(".datagrid-inline-edit-trigger").removeClass("hidden")):e._datagrid_inline_edit_cancel?t.find(".datagrid-inline-edit-trigger").removeClass("hidden"):void 0}}),a(document).on("mouseup","[data-datagrid-cancel-inline-add]",(function(e){if(1===(e.which||e.keyCode||0))return e.stopPropagation(),e.preventDefault(),a(".datagrid-row-inline-add").addClass("datagrid-row-inline-add-hidden")})),e("datagrid-toggle-inline-add",{success:function(e){var t=a(".datagrid-"+e._datagrid_name);if(e._datagrid_inline_adding){var n=t.find(".datagrid-row-inline-add");n.hasClass("datagrid-row-inline-add-hidden")&&n.removeClass("datagrid-row-inline-add-hidden"),n.find("input:not([readonly]),textarea:not([readonly])").first().focus()}}}),u=function(){var e=a(".selectpicker").first();if(a.fn.selectpicker)return a.fn.selectpicker.defaults={countSelectedText:e.data("i18n-selected"),iconBase:"",tickIcon:e.data("selected-icon-check")}},a((function(){return u()})),d=function(){if(a.fn.selectpicker)return a("[data-datagrid-multiselect-id]").each((function(){var e;if(a(this).hasClass("selectpicker"))return a(this).removeAttr("id"),e=a(this).data("datagrid-multiselect-id"),a(this).on("loaded.bs.select",(function(e){return a(this).parent().attr("style","display:none;"),a(this).parent().find(".hidden").removeClass("hidden").addClass("btn-default btn-secondary")})),a(this).on("rendered.bs.select",(function(t){return a(this).parent().attr("id",e)}))}))},a((function(){return d()})),e("datagrid.fitlerMultiSelect",{success:function(){if(u(),a.fn.selectpicker)return a(".selectpicker").selectpicker({iconBase:"fa"})}}),e("datagrid.groupActionMultiSelect",{success:function(){return d()}}),e("datagrid.inline-editing",{success:function(e){if(e._datagrid_inline_editing)return a(".datagrid-"+e._datagrid_name).find(".datagrid-inline-edit-trigger").addClass("hidden")}}),e("datagrid.redraw-item",{success:function(e){if(e._datagrid_redraw_item_class)return a("tr[data-id="+e._datagrid_redraw_item_id+"]").attr("class",e._datagrid_redraw_item_class)}}),e("datagrid.reset-filter-by-column",{success:function(e){var t,n,r,i,o,s;if(e._datagrid_name&&((t=a(".datagrid-"+e._datagrid_name)).find("[data-datagrid-reset-filter-by-column]").addClass("hidden"),e.non_empty_filters&&e.non_empty_filters.length)){for(r=0,o=(s=e.non_empty_filters).length;r<o;r++)i=s[r],t.find("[data-datagrid-reset-filter-by-column="+i+"]").removeClass("hidden");return n=t.find(".reset-filter").attr("href"),t.find("[data-datagrid-reset-filter-by-column]").each((function(){var t;return i=a(this).attr("data-datagrid-reset-filter-by-column"),t=n.replace("do="+e._datagrid_name+"-resetFilter","do="+e._datagrid_name+"-resetColumnFilter"),t+="&"+e._datagrid_name+"-key="+i,a(this).attr("href",t)}))}}})}()}();
//# sourceMappingURL=datagrid.5a83018c.bundle.js.map