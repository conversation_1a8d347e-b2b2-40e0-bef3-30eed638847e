/*! For license information please see admin.68a6fe11.bundle.js.LICENSE.txt */
!function(){var t={809:function(t){t.exports=function(){"use strict";class t{filled(t,e,i){return""!==i&&!1!==i&&null!==i&&(!Array.isArray(i)||i.length>0)&&(!(i instanceof FileList)||i.length>0)}blank(t,e,i){return!this.filled(t,e,i)}valid(t,e){return e.validateControl(t,void 0,!0)}equal(t,e,i){if(void 0===e)return null;let n=t=>"number"==typeof t||"string"==typeof t?""+t:!0===t?"1":"",r=Array.isArray(i)?i:[i],a=Array.isArray(e)?e:[e];t:for(let t of r){for(let e of a)if(n(t)===n(e))continue t;return!1}return r.length>0}notEqual(t,e,i){return void 0===e?null:!this.equal(t,e,i)}minLength(t,e,i){return(i="number"==typeof i?i.toString():i).length>=e}maxLength(t,e,i){return(i="number"==typeof i?i.toString():i).length<=e}length(t,e,i){return i="number"==typeof i?i.toString():i,(null===(e=Array.isArray(e)?e:[e,e])[0]||i.length>=e[0])&&(null===e[1]||i.length<=e[1])}email(t,e,i){return/^("([ !#-[\]-~]|\\[ -~])+"|[-a-z0-9!#$%&'*+/=?^_`{|}~]+(\.[-a-z0-9!#$%&'*+/=?^_`{|}~]+)*)@([0-9a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,61}[0-9a-z\u00C0-\u02FF\u0370-\u1EFF])?\.)+[a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,17}[a-z\u00C0-\u02FF\u0370-\u1EFF])?$/i.test(i)}url(t,e,i,n){return/^[a-z\d+.-]+:/.test(i)||(i="https://"+i),!!/^https?:\/\/((([-_0-9a-z\u00C0-\u02FF\u0370-\u1EFF]+\.)*[0-9a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,61}[0-9a-z\u00C0-\u02FF\u0370-\u1EFF])?\.)?[a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,17}[a-z\u00C0-\u02FF\u0370-\u1EFF])?|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|\[[0-9a-f:]{3,39}\])(:\d{1,5})?(\/\S*)?$/i.test(i)&&(n.value=i,!0)}regexp(t,e,i){let n="string"==typeof e&&e.match(/^\/(.*)\/([imu]*)$/);try{return n&&new RegExp(n[1],n[2].replace("u","")).test(i)}catch{return null}}pattern(t,e,i,n,r){if("string"!=typeof e)return null;try{let t;try{t=new RegExp("^(?:"+e+")$",r?"ui":"u")}catch{t=new RegExp("^(?:"+e+")$",r?"i":"")}return i instanceof FileList?Array.from(i).every((e=>t.test(e.name))):t.test(i)}catch{return null}}patternCaseInsensitive(t,e,i){return this.pattern(t,e,i,null,!0)}numeric(t,e,i){return/^[0-9]+$/.test(i)}integer(t,e,i,n){return!!/^-?[0-9]+$/.test(i)&&(n.value=parseFloat(i),!0)}float(t,e,i,n){return i=i.replace(/ +/g,"").replace(/,/g,"."),!!/^-?[0-9]*\.?[0-9]+$/.test(i)&&(n.value=parseFloat(i),!0)}min(t,e,i){return Number.isFinite(e)&&(i=parseFloat(i)),i>=e}max(t,e,i){return Number.isFinite(e)&&(i=parseFloat(i)),i<=e}range(t,e,i){return Array.isArray(e)?"time"===t.type&&e[0]>e[1]?i>=e[0]||i<=e[1]:(null===e[0]||this.min(t,e[0],i))&&(null===e[1]||this.max(t,e[1],i)):null}submitted(t){return t.form["nette-submittedBy"]===t}fileSize(t,e,i){return Array.from(i).every((t=>t.size<=e))}mimeType(t,e,i){let n=[];(e=Array.isArray(e)?e:[e]).forEach((t=>n.push("^"+t.replace(/([^\w])/g,"\\$1").replace("\\*",".*")+"$")));let r=new RegExp(n.join("|"));return Array.from(i).every((t=>!t.type||r.test(t.type)))}image(t,e,i){return this.mimeType(t,e??["image/gif","image/png","image/jpeg","image/webp"],i)}static(t,e){return e}}let e={"á":"a","ä":"a","č":"c","ď":"d","é":"e","ě":"e","í":"i","ľ":"l","ň":"n","ó":"o","ô":"o","ř":"r","š":"s","ť":"t","ú":"u","ů":"u","ý":"y","ž":"z"};let i=new class{formErrors=[];validators=new t;#t={};#e={};#i=new WeakMap;#n(t,e){let i=t.elements.namedItem(e);return i instanceof RadioNodeList?i[0]:i}#r(t){let e=t.form.elements.namedItem(t.name);return e instanceof RadioNodeList?Array.from(e):[e]}#a(t){"loading"!==document.readyState?t.call(this):document.addEventListener("DOMContentLoaded",t)}getValue(t){return t instanceof HTMLInputElement?"radio"===t.type?this.#r(t).find((t=>t.checked))?.value??null:"file"===t.type?t.files:"checkbox"===t.type?t.name.endsWith("[]")?this.#r(t).filter((t=>t.checked)).map((t=>t.value)):t.checked:t.value.trim():t instanceof HTMLSelectElement?t.multiple?Array.from(t.selectedOptions,(t=>t.value)):t.selectedOptions[0]?.value??null:t instanceof HTMLTextAreaElement?t.value:t instanceof RadioNodeList?this.getValue(t[0]):null}getEffectiveValue(t,e=!1){let i=this.getValue(t);if(i===t.getAttribute("data-nette-empty-value")&&(i=""),e&&void 0===this.#t[t.name]){this.#t[t.name]=!0;let e={value:i};this.validateControl(t,void 0,!0,e),i=e.value,delete this.#t[t.name]}return i}validateControl(t,e,i=!1,n,r){e??=JSON.parse(t.getAttribute("data-nette-rules")??"[]"),n??={value:this.getEffectiveValue(t)},r??=!this.validateRule(t,":filled",null,n);for(let a of e){let e=a.op.match(/(~)?([^?]+)/),s=a.control?this.#n(t.form,a.control):t;if(a.neg=!!e[1],a.op=e[2],a.condition=!!a.rules,!s)continue;if(r&&!a.condition&&":filled"!==a.op)continue;let o=this.validateRule(s,a.op,a.arg,t===s?n:void 0);if(null!==o)if(a.neg&&(o=!o),a.condition&&o){if(!this.validateControl(t,a.rules,i,n,":blank"!==a.op&&r))return!1}else if(!a.condition&&!o){if(this.isDisabled(s))continue;if(!i){let e=Array.isArray(a.arg)?a.arg:[a.arg],i=a.msg.replace(/%(value|\d+)/g,((i,n)=>this.getValue("value"===n?s:t.form.elements.namedItem(e[n].control))));this.addError(s,i)}return!1}}return!0}validateForm(t,e=!1){let i,n=t.form??t;if(this.formErrors=[],null!==t.getAttribute("formnovalidate")){let e=JSON.parse(t.getAttribute("data-nette-validation-scope")??"[]");if(!e.length)return this.showFormErrors(n,[]),!0;i=new RegExp("^("+e.join("-|")+"-)")}for(let t of n.elements)if(t.willValidate&&t.validity.badInput)return t.reportValidity(),!1;for(let t of n.elements)if(t.getAttribute("data-nette-rules")&&(!i||t.name.replace(/]\[|\[|]|$/g,"-").match(i))&&!this.isDisabled(t)&&!this.validateControl(t,void 0,e)&&!this.formErrors.length)return!1;let r=!this.formErrors.length;return this.showFormErrors(n,this.formErrors),r}isDisabled(t){return"radio"===t.type?this.#r(t).every((t=>t.disabled)):t.disabled}addError(t,e){this.formErrors.push({element:t,message:e})}showFormErrors(t,e){let i,n=[];for(let t of e)n.indexOf(t.message)<0&&(n.push(t.message),i??=t.element);n.length&&this.showModal(n.join("\n"),(()=>{i?.focus()}))}showModal(t,e){let i=document.createElement("dialog");if(!i.showModal)return alert(t),void e();let n=document.createElement("style");n.innerText=".netteFormsModal { text-align: center; margin: auto; border: 2px solid black; padding: 1rem } .netteFormsModal button { padding: .1em 2em }";let r=document.createElement("button");r.innerText="OK",r.onclick=()=>{i.remove(),e()},i.setAttribute("class","netteFormsModal"),i.innerText=t+"\n\n",i.append(n,r),document.body.append(i),i.showModal()}validateRule(t,e,i,n){if(t.validity.badInput)return":filled"===e;n??={value:this.getEffectiveValue(t,!0)};let r=":"===e.charAt(0)?e.substring(1):e;r=r.replace("::","_").replaceAll("\\","");let a=Array.isArray(i)?i:[i];return a=a.map((e=>{if(e?.control){let i=this.#n(t.form,e.control);return i===t?n.value:this.getEffectiveValue(i,!0)}return e})),"valid"===r&&(a[0]=this),this.validators[r]?this.validators[r](t,Array.isArray(i)?a:a[0],n.value,n):null}toggleForm(t,e){this.#e={};for(let i of Array.from(t.elements))i.getAttribute("data-nette-rules")&&this.toggleControl(i,void 0,null,!e);for(let t in this.#e)this.toggle(t,this.#e[t].state,this.#e[t].elem,e)}toggleControl(t,e,i=null,n=!1,r,a){e??=JSON.parse(t.getAttribute("data-nette-rules")??"[]"),r??={value:this.getEffectiveValue(t)},a??=!this.validateRule(t,":filled",null,r);let s,o=!1;for(let l of e){let e=l.op.match(/(~)?([^?]+)/),d=l.control?this.#n(t.form,l.control):t;if(l.neg=!!e[1],l.op=e[2],l.condition=!!l.rules,d&&(!a||l.condition||":filled"===l.op)){if(s=i,!1!==i){if(s=this.validateRule(d,l.op,l.arg,t===d?r:void 0),null===s)continue;l.neg&&(s=!s),l.condition||(i=s)}if(l.condition&&this.toggleControl(t,l.rules,s,n,r,":blank"!==l.op&&a)||l.toggle){o=!0,n&&this.#r(d).filter((t=>!this.#i.has(t))).forEach((e=>{e.addEventListener("change",(e=>this.toggleForm(t.form,e))),this.#i.set(e,null)}));for(let e in l.toggle??{})this.#e[e]??={elem:t,state:!1},this.#e[e].state||=l.toggle[e]?!!s:!s}}}return o}toggle(t,e,i,n){/^\w[\w.:-]*$/.test(t)&&(t="#"+t),Array.from(document.querySelectorAll(t)).forEach((t=>t.hidden=!e))}compactCheckboxes(t,e){let i={};for(let n of t.elements)n instanceof HTMLInputElement&&"checkbox"===n.type&&n.name.endsWith("[]")&&n.checked&&!n.disabled&&(e.delete(n.name),i[n.name]??=[],i[n.name].push(n.value));for(let t in i)e.set(t.substring(0,t.length-2),i[t].join(","))}initForm(t){"get"===t.method&&t.hasAttribute("data-nette-compact")&&t.addEventListener("formdata",(e=>this.compactCheckboxes(t,e.formData))),Array.from(t.elements).some((t=>t.getAttribute("data-nette-rules")))&&(this.toggleForm(t),t.noValidate||(t.noValidate=!0,t.addEventListener("submit",(e=>{this.validateForm(e.submitter||t)||(e.stopImmediatePropagation(),e.preventDefault())})),t.addEventListener("reset",(()=>{setTimeout((()=>this.toggleForm(t)))}))))}initOnLoad(){this.#a((()=>{Array.from(document.forms).forEach((t=>this.initForm(t)))}))}};return i.version="3.5.3",i.webalize=function(t){t=t.toLowerCase();let i="";for(let n=0;n<t.length;n++){i+=e[t.charAt(n)]||t.charAt(n)}return i.replace(/[^a-z0-9]+/g,"-").replace(/^-|-$/g,"")},i}()}},e={};function i(n){var r=e[n];if(void 0!==r)return r.exports;var a=e[n]={exports:{}};return t[n].call(a.exports,a,a.exports,i),a.exports}!function(){"use strict";const t=t=>{"loading"===document.readyState?document.addEventListener("DOMContentLoaded",t):t()};class e extends Error{}const n=(t,i)=>{if(!t)throw new e("Assertion failed"+(void 0!==i?`: ${i}`:"."))};class r extends EventTarget{constructor(t){super(),this.naja=t,this.selector=".ajax",this.allowedOrigins=[window.location.origin],this.handler=this.handleUI.bind(this),t.addEventListener("init",this.initialize.bind(this))}initialize(){t((()=>this.bindUI(window.document.body))),this.naja.snippetHandler.addEventListener("afterUpdate",(t=>{const{snippet:e}=t.detail;this.bindUI(e)}))}bindUI(t){const e=[`a${this.selector}`,`input[type="submit"]${this.selector}`,`input[type="image"]${this.selector}`,`button[type="submit"]${this.selector}`,`form${this.selector} input[type="submit"]`,`form${this.selector} input[type="image"]`,`form${this.selector} button[type="submit"]`].join(", "),i=t=>{t.removeEventListener("click",this.handler),t.addEventListener("click",this.handler)},n=t.querySelectorAll(e);for(let t=0;t<n.length;t++)i(n.item(t));t.matches(e)&&i(t);const r=t=>{t.removeEventListener("submit",this.handler),t.addEventListener("submit",this.handler)};t.matches(`form${this.selector}`)&&r(t);const a=t.querySelectorAll(`form${this.selector}`);for(let t=0;t<a.length;t++)r(a.item(t))}handleUI(t){const e=t;if(e.altKey||e.ctrlKey||e.shiftKey||e.metaKey||e.button)return;const i=t.currentTarget,n=this.naja.prepareOptions(),r=()=>{};"submit"===t.type?this.submitForm(i,n,t).catch(r):"click"===t.type&&this.clickElement(i,n,e).catch(r)}async clickElement(t,e={},i){let r,a="GET",s="";if(!this.dispatchEvent(new CustomEvent("interaction",{cancelable:!0,detail:{element:t,originalEvent:i,options:e}})))return i?.preventDefault(),{};if("A"===t.tagName)n(t instanceof HTMLAnchorElement),a="GET",s=t.href,r=null;else if("INPUT"===t.tagName||"BUTTON"===t.tagName){n(t instanceof HTMLInputElement||t instanceof HTMLButtonElement);const{form:e}=t;if(a=t.getAttribute("formmethod")?.toUpperCase()??e?.getAttribute("method")?.toUpperCase()??"GET",s=t.getAttribute("formaction")??e?.getAttribute("action")??window.location.pathname+window.location.search,r=new FormData(e??void 0),"submit"===t.type&&""!==t.name)r.append(t.name,t.value||"");else if("image"===t.type){const e=t.getBoundingClientRect(),n=""!==t.name?`${t.name}.`:"";r.append(`${n}x`,Math.max(0,Math.floor(void 0!==i?i.pageX-e.left:0))),r.append(`${n}y`,Math.max(0,Math.floor(void 0!==i?i.pageY-e.top:0)))}}if(!this.isUrlAllowed(s))throw new Error(`Cannot dispatch async request, URL is not allowed: ${s}`);return i?.preventDefault(),this.naja.makeRequest(a,s,r,e)}async submitForm(t,e={},i){if(!this.dispatchEvent(new CustomEvent("interaction",{cancelable:!0,detail:{element:t,originalEvent:i,options:e}})))return i?.preventDefault(),{};const n=t.getAttribute("method")?.toUpperCase()??"GET",r=t.getAttribute("action")??window.location.pathname+window.location.search,a=new FormData(t);if(!this.isUrlAllowed(r))throw new Error(`Cannot dispatch async request, URL is not allowed: ${r}`);return i?.preventDefault(),this.naja.makeRequest(n,r,a,e)}isUrlAllowed(t){const e=new URL(t,location.href);return"null"!==e.origin&&this.allowedOrigins.includes(e.origin)}}class a{constructor(t){this.naja=t,t.addEventListener("init",this.initialize.bind(this)),t.uiHandler.addEventListener("interaction",this.processForm.bind(this))}initialize(){t((()=>this.initForms(window.document.body))),this.naja.snippetHandler.addEventListener("afterUpdate",(t=>{const{snippet:e}=t.detail;this.initForms(e)}))}initForms(t){const e=this.netteForms||window.Nette;if(e){"form"===t.tagName&&e.initForm(t);const i=t.querySelectorAll("form");for(let t=0;t<i.length;t++)e.initForm(i.item(t))}}processForm(t){const{element:e,originalEvent:i}=t.detail,n=e;void 0!==n.form&&null!==n.form&&(n.form["nette-submittedBy"]=e);const r=this.netteForms||window.Nette;"FORM"!==e.tagName&&!e.form||!r||r.validateForm(e)||(i&&(i.stopImmediatePropagation(),i.preventDefault()),t.preventDefault())}}class s extends EventTarget{constructor(t){super(),this.naja=t,t.uiHandler.addEventListener("interaction",(t=>{const{element:e,options:i}=t.detail;if(e&&(e.hasAttribute("data-naja-force-redirect")||e.form?.hasAttribute("data-naja-force-redirect"))){const t=e.getAttribute("data-naja-force-redirect")??e.form?.getAttribute("data-naja-force-redirect");i.forceRedirect="off"!==t}})),t.addEventListener("success",(t=>{const{payload:e,options:i}=t.detail;e.redirect&&(this.makeRedirect(e.redirect,i.forceRedirect??!1,i),t.stopImmediatePropagation())})),this.locationAdapter={assign:t=>window.location.assign(t)}}makeRedirect(t,e,i={}){t instanceof URL&&(t=t.href);let n=e||!this.naja.uiHandler.isUrlAllowed(t);this.dispatchEvent(new CustomEvent("redirect",{cancelable:!0,detail:{url:t,setUrl(e){t=e},isHardRedirect:n,setHardRedirect(t){n=!!t},options:i}}))&&(n?this.locationAdapter.assign(t):this.naja.makeRequest("GET",t,null,i))}}class o extends EventTarget{constructor(t){super(),this.naja=t,this.op={replace:(t,e)=>{t.innerHTML=e},prepend:(t,e)=>t.insertAdjacentHTML("afterbegin",e),append:(t,e)=>t.insertAdjacentHTML("beforeend",e)},t.addEventListener("success",(t=>{const{options:e,payload:i}=t.detail;i.snippets&&this.updateSnippets(i.snippets,!1,e)}))}static findSnippets(t){const e={},i=window.document.querySelectorAll('[id^="snippet-"]');for(let n=0;n<i.length;n++){const r=i.item(n);(t?.(r)??1)&&(e[r.id]=r.innerHTML)}return e}updateSnippets(t,e=!1,i={}){Object.keys(t).forEach((n=>{const r=document.getElementById(n);r&&this.updateSnippet(r,t[n],e,i)}))}updateSnippet(t,e,i,n){let r=this.op.replace;!t.hasAttribute("data-naja-snippet-prepend")&&!t.hasAttribute("data-ajax-prepend")||i?!t.hasAttribute("data-naja-snippet-append")&&!t.hasAttribute("data-ajax-append")||i||(r=this.op.append):r=this.op.prepend,this.dispatchEvent(new CustomEvent("beforeUpdate",{cancelable:!0,detail:{snippet:t,content:e,fromCache:i,operation:r,changeOperation(t){r=t},options:n}}))&&("title"===t.tagName.toLowerCase()?document.title=e:r(t,e),this.dispatchEvent(new CustomEvent("afterUpdate",{cancelable:!0,detail:{snippet:t,content:e,fromCache:i,operation:r,options:n}})))}}class l extends EventTarget{constructor(t){super(),this.naja=t,this.initialized=!1,this.cursor=0,this.popStateHandler=this.handlePopState.bind(this),t.addEventListener("init",this.initialize.bind(this)),t.addEventListener("before",this.saveUrl.bind(this)),t.addEventListener("before",this.replaceInitialState.bind(this)),t.addEventListener("success",this.pushNewState.bind(this)),t.redirectHandler.addEventListener("redirect",this.saveRedirectedUrl.bind(this)),t.uiHandler.addEventListener("interaction",this.configureMode.bind(this)),this.historyAdapter={replaceState:(t,e,i)=>window.history.replaceState(t,e,i),pushState:(t,e,i)=>window.history.pushState(t,e,i)}}set uiCache(t){console.warn("Naja: HistoryHandler.uiCache is deprecated, use options.snippetCache instead."),this.naja.defaultOptions.snippetCache=t}handlePopState(t){const{state:e}=t;if("naja"!==e?.source)return;const i=e.cursor-this.cursor;this.cursor=e.cursor;const n=this.naja.prepareOptions();this.dispatchEvent(new CustomEvent("restoreState",{detail:{state:e,direction:i,options:n}}))}initialize(){window.addEventListener("popstate",this.popStateHandler)}saveUrl(t){const{url:e,options:i}=t.detail;i.href??=e}saveRedirectedUrl(t){const{url:e,options:i}=t.detail;i.href=e}replaceInitialState(e){const{options:i}=e.detail;!1===l.normalizeMode(i.history)||this.initialized||(t((()=>this.historyAdapter.replaceState(this.buildState(window.location.href,"replace",this.cursor,i),window.document.title,window.location.href))),this.initialized=!0)}configureMode(t){const{element:e,options:i}=t.detail;if(e&&(e.hasAttribute("data-naja-history")||e.form?.hasAttribute("data-naja-history"))){const t=e.getAttribute("data-naja-history")??e.form?.getAttribute("data-naja-history");i.history=l.normalizeMode(t)}}static normalizeMode(t){return"off"!==t&&!1!==t&&("replace"!==t||"replace")}pushNewState(t){const{payload:e,options:i}=t.detail,n=l.normalizeMode(i.history);if(!1===n)return;e.postGet&&e.url&&(i.href=e.url);const r="replace"===n?"replaceState":"pushState",a="replace"===n?this.cursor:++this.cursor;this.historyAdapter[r](this.buildState(i.href,n,a,i),window.document.title,i.href)}buildState(t,e,i,n){const r={source:"naja",cursor:i,href:t};return this.dispatchEvent(new CustomEvent("buildState",{detail:{state:r,operation:"replace"===e?"replaceState":"pushState",options:n}})),r}}class d extends EventTarget{constructor(t){super(),this.naja=t,this.storages={off:new c(t),history:new u,session:new h},t.uiHandler.addEventListener("interaction",this.configureCache.bind(this)),t.historyHandler.addEventListener("buildState",this.buildHistoryState.bind(this)),t.historyHandler.addEventListener("restoreState",this.restoreHistoryState.bind(this))}resolveStorage(t){let e;return e=!0===t||void 0===t?"history":!1===t?"off":t,this.storages[e]}configureCache(t){const{element:e,options:i}=t.detail;if(e&&(e.hasAttribute("data-naja-snippet-cache")||e.form?.hasAttribute("data-naja-snippet-cache")||e.hasAttribute("data-naja-history-cache")||e.form?.hasAttribute("data-naja-history-cache"))){const t=e.getAttribute("data-naja-snippet-cache")??e.form?.getAttribute("data-naja-snippet-cache")??e.getAttribute("data-naja-history-cache")??e.form?.getAttribute("data-naja-history-cache");i.snippetCache=t}}buildHistoryState(t){const{state:e,options:i}=t.detail;"historyUiCache"in i&&(console.warn("Naja: options.historyUiCache is deprecated, use options.snippetCache instead."),i.snippetCache=i.historyUiCache);const n=o.findSnippets((t=>!(t.hasAttribute("data-naja-history-nocache")||t.hasAttribute("data-history-nocache")||t.hasAttribute("data-naja-snippet-cache")&&"off"===t.getAttribute("data-naja-snippet-cache"))));if(!this.dispatchEvent(new CustomEvent("store",{cancelable:!0,detail:{snippets:n,state:e,options:i}})))return;const r=this.resolveStorage(i.snippetCache);e.snippets={storage:r.type,key:r.store(n)}}restoreHistoryState(t){const{state:e,options:i}=t.detail;if(void 0===e.snippets)return;if(i.snippetCache=e.snippets.storage,!this.dispatchEvent(new CustomEvent("fetch",{cancelable:!0,detail:{state:e,options:i}})))return;const n=this.resolveStorage(i.snippetCache).fetch(e.snippets.key,e,i);null!==n&&this.dispatchEvent(new CustomEvent("restore",{cancelable:!0,detail:{snippets:n,state:e,options:i}}))&&this.naja.snippetHandler.updateSnippets(n,!0,i)}}class c{constructor(t){this.naja=t,this.type="off"}store(){return null}fetch(t,e,i){return this.naja.makeRequest("GET",e.href,null,{...i,history:!1,snippetCache:!1}),null}}class u{constructor(){this.type="history"}store(t){return t}fetch(t){return t}}class h{constructor(){this.type="session"}store(t){const e=Math.random().toString(36).substring(2,8);return window.sessionStorage.setItem(e,JSON.stringify(t)),e}fetch(t){const e=window.sessionStorage.getItem(t);return null===e?null:JSON.parse(e)}}class p{constructor(e){this.loadedScripts=new Set,e.addEventListener("init",(()=>{t((()=>{document.querySelectorAll("script[data-naja-script-id]").forEach((t=>{const e=t.getAttribute("data-naja-script-id");null!==e&&""!==e&&this.loadedScripts.add(e)}))})),e.snippetHandler.addEventListener("afterUpdate",(t=>{const{content:e}=t.detail;this.loadScripts(e)}))}))}loadScripts(t){"string"!=typeof t?Object.keys(t).forEach((e=>{const i=t[e];this.loadScriptsInSnippet(i)})):this.loadScriptsInSnippet(t)}loadScriptsInSnippet(t){if(!/<script/i.test(t))return;const e=window.document.createElement("div");e.innerHTML=t;const i=e.querySelectorAll("script");for(let t=0;t<i.length;t++){const e=i.item(t),n=e.getAttribute("data-naja-script-id");if(null!==n&&""!==n&&this.loadedScripts.has(n))continue;const r=window.document.createElement("script");if(r.innerHTML=e.innerHTML,e.hasAttributes()){const t=e.attributes;for(let e=0;e<t.length;e++){const i=t[e].name;r.setAttribute(i,t[e].value)}}window.document.head.appendChild(r).parentNode.removeChild(r),null!==n&&""!==n&&this.loadedScripts.add(n)}}}class m extends EventTarget{constructor(t,e,i,n,c,u,h){super(),this.VERSION=2,this.initialized=!1,this.extensions=[],this.defaultOptions={},this.uiHandler=new(t??r)(this),this.redirectHandler=new(e??s)(this),this.snippetHandler=new(i??o)(this),this.formsHandler=new(n??a)(this),this.historyHandler=new(c??l)(this),this.snippetCache=new(u??d)(this),this.scriptLoader=new(h??p)(this)}registerExtension(t){this.initialized&&t.initialize(this),this.extensions.push(t)}initialize(t={}){if(this.initialized)throw new Error("Cannot initialize Naja, it is already initialized.");this.defaultOptions=this.prepareOptions(t),this.extensions.forEach((t=>t.initialize(this))),this.dispatchEvent(new CustomEvent("init",{detail:{defaultOptions:this.defaultOptions}})),this.initialized=!0}prepareOptions(t){return{...this.defaultOptions,...t,fetch:{...this.defaultOptions.fetch,...t?.fetch}}}async makeRequest(t,e,i=null,n={}){"string"==typeof e&&(e=new URL(e,location.href)),n=this.prepareOptions(n);const r=new Headers(n.fetch.headers||{}),a=this.transformData(e,t,i),s=new AbortController,o=new Request(e.toString(),{credentials:"same-origin",...n.fetch,method:t,headers:r,body:a,signal:s.signal});if(o.headers.set("X-Requested-With","XMLHttpRequest"),o.headers.set("Accept","application/json"),!this.dispatchEvent(new CustomEvent("before",{cancelable:!0,detail:{request:o,method:t,url:e.toString(),data:i,options:n}})))return{};const l=window.fetch(o);let d,c;this.dispatchEvent(new CustomEvent("start",{detail:{request:o,promise:l,abortController:s,options:n}}));try{if(d=await l,!d.ok)throw new f(d);c=await d.json()}catch(t){if("AbortError"===t.name)return this.dispatchEvent(new CustomEvent("abort",{detail:{request:o,error:t,options:n}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:o,response:d,payload:void 0,error:t,options:n}})),{};throw this.dispatchEvent(new CustomEvent("error",{detail:{request:o,response:d,error:t,options:n}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:o,response:d,payload:void 0,error:t,options:n}})),t}return this.dispatchEvent(new CustomEvent("payload",{detail:{request:o,response:d,payload:c,options:n}})),this.dispatchEvent(new CustomEvent("success",{detail:{request:o,response:d,payload:c,options:n}})),this.dispatchEvent(new CustomEvent("complete",{detail:{request:o,response:d,payload:c,error:void 0,options:n}})),c}appendToQueryString(t,e,i){if(null!=i)if(Array.isArray(i)||Object.getPrototypeOf(i)===Object.prototype)for(const[n,r]of Object.entries(i))this.appendToQueryString(t,`${e}[${n}]`,r);else t.append(e,String(i))}transformData(t,e,i){const n=["GET","HEAD"].includes(e.toUpperCase());if(n&&i instanceof FormData){for(const[e,n]of i)null!=n&&t.searchParams.append(e,String(n));return null}if(null!==i&&Object.getPrototypeOf(i)===Object.prototype||Array.isArray(i)){const e=n?t.searchParams:new URLSearchParams;for(const[t,n]of Object.entries(i))this.appendToQueryString(e,t,n);return n?null:e}return i}}class f extends Error{constructor(t){const e=`HTTP ${t.status}: ${t.statusText}`;super(e),this.name=this.constructor.name,this.stack=new Error(e).stack,this.response=t}}const g=new m;g.registerExtension(new class{constructor(){this.abortControllers=new Set}initialize(t){t.uiHandler.addEventListener("interaction",this.checkAbortable.bind(this)),t.addEventListener("init",this.onInitialize.bind(this)),t.addEventListener("start",this.saveAbortController.bind(this)),t.addEventListener("complete",this.removeAbortController.bind(this))}onInitialize(){document.addEventListener("keydown",(t=>{if("Escape"===t.key&&!(t.ctrlKey||t.shiftKey||t.altKey||t.metaKey)){for(const t of this.abortControllers)t.abort();this.abortControllers.clear()}}))}checkAbortable(t){const{element:e,options:i}=t.detail;(e.hasAttribute("data-naja-abort")||e.form?.hasAttribute("data-naja-abort"))&&(i.abort="off"!==(e.getAttribute("data-naja-abort")??e.form?.getAttribute("data-naja-abort")))}saveAbortController(t){const{abortController:e,options:i}=t.detail;!1!==i.abort&&(this.abortControllers.add(e),i.clearAbortExtension=()=>this.abortControllers.delete(e))}removeAbortController(t){const{options:e}=t.detail;!1!==e.abort&&e.clearAbortExtension&&e.clearAbortExtension()}}),g.registerExtension(new class{constructor(){this.abortControllers=new Map}initialize(t){t.uiHandler.addEventListener("interaction",this.checkUniqueness.bind(this)),t.addEventListener("start",this.abortPreviousRequest.bind(this)),t.addEventListener("complete",this.clearRequest.bind(this))}checkUniqueness(t){const{element:e,options:i}=t.detail;if(e.hasAttribute("data-naja-unique")??e.form?.hasAttribute("data-naja-unique")){const t=e.getAttribute("data-naja-unique")??e.form?.getAttribute("data-naja-unique");i.unique="off"!==t&&(t??"default")}}abortPreviousRequest(t){const{abortController:e,options:i}=t.detail;!1!==i.unique&&(this.abortControllers.get(i.unique??"default")?.abort(),this.abortControllers.set(i.unique??"default",e))}clearRequest(t){const{request:e,options:i}=t.detail;e.signal.aborted||!1===i.unique||this.abortControllers.delete(i.unique??"default")}});var b=i(809);window.Nette=b,g.formsHandler.netteForms=b,document.addEventListener("DOMContentLoaded",g.initialize.bind(g))}()}();
//# sourceMappingURL=admin.68a6fe11.bundle.js.map