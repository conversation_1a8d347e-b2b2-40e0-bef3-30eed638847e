stages:
  - .pre
  - lint
  - build
  - test
  - deploy

variables:
  # Docker images with specific versions for stability and security
  IMAGE_PHP: registry.kickme.cz/docker/ci-cd/php:8.3-ci
  IMAGE_NODE: registry.kickme.cz/docker/ci-cd/npm:20.3.1
  IMAGE_DEPLOY: registry.kickme.cz/docker/ci-cd/deploy:rsync
  IMAGE_GODOT: registry.kickme.cz/docker/ci-cd/godot:4.4.1
  IMAGE_DOCKER: docker:dind

  # GitLab CI optimization settings
  FF_USE_FASTZIP: "true" # enable fastzip - a faster zip implementation that also supports level configuration
  ARTIFACT_COMPRESSION_LEVEL: fast # can also be set to fastest, fast, slow and slowest
  CACHE_COMPRESSION_LEVEL: fast # same as above, but for caches
  TRANSFER_METER_FREQUENCY: 5s # will display transfer progress every 5 seconds for artifacts and remote caches

  # Git submodule settings
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_SUBMODULE_DEPTH: 1

  # Cache directories
  npm_config_cache: "$CI_PROJECT_DIR/.npm"
  COMPOSER_CACHE_DIR: "$CI_PROJECT_DIR/.composer"

  # Docker registry settings
  DOCKER_REGISTRY: "$CI_REGISTRY"
  BACKEND_IMAGE_NAME: "$CI_REGISTRY_IMAGE/backend"
  WEB_IMAGE_NAME: "$CI_REGISTRY_IMAGE/web"

  GODOT_BUILD_DIR: "$CI_PROJECT_DIR/game/build"

# Reusable templates
.docker-login: &docker-login
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

.node-setup: &node-setup
  before_script:
    - npm config set cache $npm_config_cache

# Install dependencies
Install composer backend:
  stage: .pre
  image: $IMAGE_PHP
  tags:
    - build
  cache:
    key:
      files:
        - backend/composer.lock
    paths:
      - backend/.composer
    policy: pull-push
  artifacts:
    expire_in: 1h
    paths:
      - backend/vendor/
  script:
    - cd backend
    - composer install --no-progress --prefer-dist
    - cd ..
  only:
    refs:
      - master
    # changes:
    #   - backend/**/*

Install npm backend:
  stage: .pre
  image: $IMAGE_NODE
  tags:
    - build
  <<: *node-setup
  cache:
    key:
      files:
        - backend/package-lock.json
    paths:
      - .npm/
    policy: pull-push
  artifacts:
    expire_in: 1h
    paths:
      - backend/node_modules/
  script:
    - cd backend
    - npm ci
    - cd ..
  only:
    refs:
      - master
    # changes:
    #   - backend/**/*

Install npm web:
  stage: .pre
  image: $IMAGE_NODE
  tags:
    - build
  <<: *node-setup
  cache:
    key:
      files:
        - web/package-lock.json
    paths:
      - .npm/
    policy: pull-push
  artifacts:
    expire_in: 1h
    paths:
      - web/node_modules/
  script:
    - cd web
    - npm ci --legacy-peer-deps
    - npm install -D terser
    - cd ..
  only:
    refs:
      - master
    # changes:
    #   - web/**/*

# Linting jobs
Lint backend PHP:
  stage: lint
  image: $IMAGE_PHP
  tags:
    - build
  needs:
    - job: Install composer backend
  script:
    - cd backend
    - vendor/bin/phpstan analyse app --level=5
  allow_failure: true
  only:
    refs:
      - master
    # changes:
    #   - backend/**/*

# Build jobs
Build backend:
  stage: build
  image: $IMAGE_NODE
  tags:
    - build
  needs:
    - job: Install npm backend
  script:
    - cd backend
    - npm run build
  artifacts:
    expire_in: 1h
    paths:
      - backend/www/dist/
  only:
    refs:
      - master
    # changes:
    #   - backend/**/*

Build web:
  stage: build
  image: $IMAGE_NODE
  tags:
    - build
  needs:
    - job: Install npm web
  script:
    - cd web
    - npm run build
  artifacts:
    expire_in: 1h
    paths:
      - web/dist/
  only:
    refs:
      - master
    # changes:
    #   - web/**/*

Build Docker backend:php:
  stage: build
  image: $IMAGE_DOCKER
  needs:
    - job: Install composer backend
    - job: Build backend
  <<: *docker-login
  script:
    - cd backend && docker build -t $BACKEND_IMAGE_NAME:php .
    - docker push $BACKEND_IMAGE_NAME:php
  only:
    refs:
      - master
    # changes:
    #   - backend/**/*

Build Docker backend:nginx:
  stage: build
  image: $IMAGE_DOCKER
  needs:
    - job: Install composer backend
    - job: Build backend
  <<: *docker-login
  script:
    - cd backend && docker build -t $BACKEND_IMAGE_NAME:nginx -f Dockerfile.nginx .
    - docker push $BACKEND_IMAGE_NAME:nginx
  only:
    refs:
      - master
    # changes:
    #   - backend/**/*

Build Docker osm2pgsql:
  stage: build
  image: $IMAGE_DOCKER
  <<: *docker-login
  script:
    - cd backend && docker build -t $BACKEND_IMAGE_NAME-osm2pgsql -f Dockerfile.osm2pgsql .
    - docker push $BACKEND_IMAGE_NAME-osm2pgsql
  only:
    refs:
      - master
    # changes:
    #   - backend/Dockerfile.osm2pgsql
    #   - backend/scripts/overpass-api/*.txt

Build Docker web:
  stage: build
  image: $IMAGE_DOCKER
  needs:
    - job: Build web
  <<: *docker-login
  script:
    - cd web && docker build -t $WEB_IMAGE_NAME .
    - docker push $WEB_IMAGE_NAME
  only:
    - master

Build Godot Game:
  stage: build
  image: $IMAGE_GODOT
  allow_failure: true
  tags:
    - build
  script:
    - echo "Building Game project..."
    - mkdir -p $GODOT_BUILD_DIR/android
    # Export for Android
    - godot --path game --headless --install-android-build-template --export-release "Android" $GODOT_BUILD_DIR/android/kokume.apk
    # Note: Web export preset is not configured yet
    # - mkdir -p $GODOT_BUILD_DIR/web
    # - godot --path game --headless --export-release "Web" $GODOT_BUILD_DIR/web/index.html
  artifacts:
    expire_in: 1h
    paths:
      - game/build/
  only:
    - master
    - merge_requests

# Test jobs
# Uncomment and configure when tests are added to the project
#Test PHP:
#  stage: test
#  image: $IMAGE_PHP
#  tags:
#    - build
#  needs:
#    - job: Install composer
#  script:
#    - cd backend
#    - echo "No tests configured yet"
#    # Add this when phpunit.xml is configured:
#    # - vendor/bin/phpunit
#  allow_failure: true
#  only:
#    - master
#    - merge_requests

# Deploy jobs
Deploy:
  stage: deploy
  image: curlimages/curl:latest
  needs:
    - job: Build Docker backend:php
      optional: true
    - job: Build Docker backend:nginx
      optional: true
    - job: Build Docker web
      optional: true
  script:
    - echo "Deploying to Portainer..."
    - curl -X POST -sf https://portainer.haho.cz/api/stacks/webhooks/d794cedc-6584-48bf-b169-eeba472a6f46
    - echo "Deployment complete."