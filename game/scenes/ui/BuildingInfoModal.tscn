[gd_scene load_steps=3 format=3 uid="uid://xy0abda87654"]

[ext_resource type="PackedScene" uid="uid://cdefgh123456" path="res://scenes/ui/Modal.tscn" id="1_base_modal"]
[ext_resource type="Script" uid="uid://b6xgw2xvust8j" path="res://scripts/ui/BuildingInfoModal.gd" id="2_building_modal_script"]

[node name="BuildingInfoModal" instance=ExtResource("1_base_modal")]
script = ExtResource("2_building_modal_script")

[node name="TitleLabel" parent="MarginContainer/VBoxContainer" index="0"]
text = "Building Name"

[node name="BuildingLevelLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
text = "Level: X"

[node name="StateLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer" index="1"]
unique_name_in_owner = true
layout_mode = 2
text = "Stav: X"

[node name="StorageSection" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="2"]
unique_name_in_owner = true
layout_mode = 2

[node name="HSeparator2" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/StorageSection" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="StorageLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/StorageSection" index="1"]
unique_name_in_owner = true
layout_mode = 2
text = "Sklad:"
horizontal_alignment = 1

[node name="StorageContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/StorageSection" index="2"]
unique_name_in_owner = true
layout_mode = 2

[node name="PickupResourcesButton" type="Button" parent="MarginContainer/VBoxContainer/ContentContainer/StorageSection" index="3"]
unique_name_in_owner = true
layout_mode = 2
text = "Pick Up Resources"

[node name="UpgradeSection" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="3"]
unique_name_in_owner = true
layout_mode = 2

[node name="HSeparator3" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/UpgradeSection" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="UpgradeLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/UpgradeSection" index="1"]
layout_mode = 2
text = "Vylepšit:"
horizontal_alignment = 1

[node name="UpgradeCostContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/UpgradeSection" index="2"]
unique_name_in_owner = true
layout_mode = 2

[node name="UpgradeButton" type="Button" parent="MarginContainer/VBoxContainer/ContentContainer/UpgradeSection" index="3"]
unique_name_in_owner = true
layout_mode = 2
text = "Upgrade Building"

[node name="DemolishSection" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="4"]
unique_name_in_owner = true
layout_mode = 2

[node name="HSeparator4" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/DemolishSection" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="DemolishLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/DemolishSection" index="1"]
layout_mode = 2
text = "Zbourat:"
horizontal_alignment = 1

[node name="DemolishButton" type="Button" parent="MarginContainer/VBoxContainer/ContentContainer/DemolishSection" index="2"]
unique_name_in_owner = true
layout_mode = 2
text = "Demolish Building"

[node name="StealSection" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="5"]
unique_name_in_owner = true
layout_mode = 2

[node name="HSeparator5" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/StealSection" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="StealLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/StealSection" index="1"]
layout_mode = 2
text = "Vykrást:"
horizontal_alignment = 1

[node name="StealButton" type="Button" parent="MarginContainer/VBoxContainer/ContentContainer/StealSection" index="2"]
unique_name_in_owner = true
layout_mode = 2
text = "Steal Resources"

[node name="StealResultContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/StealSection" index="3"]
unique_name_in_owner = true
visible = false
layout_mode = 2

[node name="StealResultLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/StealSection/StealResultContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
text = "Combat Result:"
horizontal_alignment = 1

[node name="StealLootContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/StealSection/StealResultContainer" index="1"]
unique_name_in_owner = true
layout_mode = 2

[node name="HireHeroSection" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="6"]
unique_name_in_owner = true
layout_mode = 2

[node name="HSeparator6" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/HireHeroSection" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="HireHeroLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/HireHeroSection" index="1"]
layout_mode = 2
text = "Hrdina:"
horizontal_alignment = 1

[node name="HeroNameInput" type="LineEdit" parent="MarginContainer/VBoxContainer/ContentContainer/HireHeroSection" index="2"]
unique_name_in_owner = true
layout_mode = 2
placeholder_text = "Enter hero name"

[node name="HireHeroButton" type="Button" parent="MarginContainer/VBoxContainer/ContentContainer/HireHeroSection" index="3"]
unique_name_in_owner = true
layout_mode = 2
text = "Najmout hrdinu"

[node name="ProductionSection" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="7"]
unique_name_in_owner = true
layout_mode = 2

[node name="HSeparator7" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/ProductionSection" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="ProductionLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ProductionSection" index="1"]
layout_mode = 2
text = "Dostupné jednotky:"
horizontal_alignment = 1

[node name="ProductionContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ProductionSection" index="2"]
unique_name_in_owner = true
layout_mode = 2

[node name="QueueSection" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="8"]
unique_name_in_owner = true
layout_mode = 2

[node name="HSeparator8" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/QueueSection" index="0"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="QueueLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/QueueSection" index="1"]
layout_mode = 2
text = "Fronta:"
horizontal_alignment = 1

[node name="QueueContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/QueueSection" index="2"]
unique_name_in_owner = true
layout_mode = 2
