[gd_scene load_steps=3 format=3 uid="uid://c8yvxnqvxnqvg"]

[ext_resource type="PackedScene" uid="uid://cdefgh123456" path="res://scenes/ui/Modal.tscn" id="1_base_modal"]
[ext_resource type="Script" uid="uid://dvnboblnddg34" path="res://scripts/ui/HeroModal.gd" id="2_hero_modal_script"]

[node name="HeroModal" instance=ExtResource("1_base_modal")]
script = ExtResource("2_hero_modal_script")

[node name="TitleLabel" parent="MarginContainer/VBoxContainer" index="0"]
text = "Hero Details"

[node name="ScrollContainer" type="ScrollContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="0"]
layout_mode = 2
size_flags_vertical = 3
horizontal_scroll_mode = 0

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer" index="0"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 15

[node name="HeroHeader" type="HBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="0"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="HeroPortrait" type="TextureRect" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/HeroHeader" index="0"]
unique_name_in_owner = true
custom_minimum_size = Vector2(120, 120)
layout_mode = 2
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/HeroHeader" index="1"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HeroNameLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/HeroHeader/VBoxContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
text = "Hero Name"
horizontal_alignment = 1

[node name="HeroLevelLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/HeroHeader/VBoxContainer" index="1"]
unique_name_in_owner = true
layout_mode = 2
text = "Level: 1"
horizontal_alignment = 1

[node name="HeroDescriptionLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/HeroHeader/VBoxContainer" index="2"]
unique_name_in_owner = true
layout_mode = 2
text = "Hero description goes here..."
horizontal_alignment = 1
autowrap_mode = 3

[node name="HSeparator" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="1"]
layout_mode = 2

[node name="StatsLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="2"]
layout_mode = 2
text = "Statistics"
horizontal_alignment = 1

[node name="StatsContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="3"]
unique_name_in_owner = true
layout_mode = 2
theme_override_constants/separation = 10

[node name="HealthStat" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer" index="0"]
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/HealthStat" index="0"]
layout_mode = 2
text = "Health"

[node name="ProgressBar" type="ProgressBar" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/HealthStat" index="1"]
layout_mode = 2
value = 50.0

[node name="AgilityStatStat" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer" index="1"]
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/AgilityStatStat" index="0"]
layout_mode = 2
text = "Agility"

[node name="ProgressBar" type="ProgressBar" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/AgilityStatStat" index="1"]
layout_mode = 2
value = 30.0

[node name="StrengthStat" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer" index="2"]
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/StrengthStat" index="0"]
layout_mode = 2
text = "Strength"

[node name="ProgressBar" type="ProgressBar" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/StrengthStat" index="1"]
layout_mode = 2
value = 40.0

[node name="DefenseStat" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer" index="3"]
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/DefenseStat" index="0"]
layout_mode = 2
text = "Defense"

[node name="ProgressBar" type="ProgressBar" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/DefenseStat" index="1"]
layout_mode = 2
value = 35.0

[node name="ExperienceStat" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer" index="4"]
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/ExperienceStat" index="0"]
unique_name_in_owner = true
layout_mode = 2
text = "Next Level (100)"

[node name="ProgressBar" type="ProgressBar" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/StatsContainer/ExperienceStat" index="1"]
layout_mode = 2
value = 25.0

[node name="HSeparator2" type="HSeparator" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="4"]
layout_mode = 2

[node name="EquipmentLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="5"]
layout_mode = 2
text = "Equipment"
horizontal_alignment = 1

[node name="EquipmentContainer" type="GridContainer" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer" index="6"]
unique_name_in_owner = true
layout_mode = 2
columns = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer/ScrollContainer/VBoxContainer/EquipmentContainer" index="0"]
layout_mode = 2
text = "Equipment will be displayed here"
