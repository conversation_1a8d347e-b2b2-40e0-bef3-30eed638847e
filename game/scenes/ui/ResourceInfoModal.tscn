[gd_scene load_steps=3 format=3 uid="uid://pqrstu123456"]

[ext_resource type="PackedScene" uid="uid://cdefgh123456" path="res://scenes/ui/Modal.tscn" id="1_base_modal"]
[ext_resource type="Script" uid="uid://cvcrjpcwvkhfw" path="res://scripts/ui/ResourceInfoModal.gd" id="2_resource_modal_script"]

[node name="ResourceInfoModal" instance=ExtResource("1_base_modal")]
script = ExtResource("2_resource_modal_script")

[node name="ResourceTypeLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
text = "Type: ..."

[node name="ResourceAmountLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer" index="1"]
unique_name_in_owner = true
layout_mode = 2
text = "Amount Available: ..."

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="2"]
layout_mode = 2
theme_override_constants/margin_top = 10

[node name="ResourceListContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/MarginContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
