[gd_scene load_steps=3 format=3 uid="uid://mnopqr24680"]

[ext_resource type="PackedScene" uid="uid://cdefgh123456" path="res://scenes/ui/Modal.tscn" id="1_base_modal"]
[ext_resource type="Script" uid="uid://dxsnyq2nbrd3k" path="res://scripts/ui/RegionInfoModal.gd" id="2_region_modal_script"]

[node name="RegionInfoModal" instance=ExtResource("1_base_modal")]
script = ExtResource("2_region_modal_script")

[node name="RegionOwnerLabel" type="Label" parent="MarginContainer/VBoxContainer/ContentContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
text = "Owner: ..."

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/VBoxContainer/ContentContainer" index="1"]
layout_mode = 2
theme_override_constants/margin_top = 10

[node name="ResourceListContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ContentContainer/MarginContainer" index="0"]
unique_name_in_owner = true
layout_mode = 2
