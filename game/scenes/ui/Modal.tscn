[gd_scene load_steps=2 format=3 uid="uid://cdefgh123456"]

[ext_resource type="Script" uid="uid://bmjrk2obt634s" path="res://scripts/ui/Modal.gd" id="1_modal_script"]

[node name="Modal" type="PanelContainer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 80.0
offset_right = -20.0
offset_bottom = -80.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_modal_script")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="TitleLabel" type="Label" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Modal Title"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="ContentContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3

[node name="HSeparator2" type="HSeparator" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="CloseButton" type="Button" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 4
custom_minimum_size = Vector2(120, 50)
text = "Close"
