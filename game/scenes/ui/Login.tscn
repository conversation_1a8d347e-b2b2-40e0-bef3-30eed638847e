[gd_scene load_steps=2 format=3 uid="uid://qfwpvk6qg0a0"]

[ext_resource type="Script" uid="uid://2vrlj3fdqrvw" path="res://scripts/ui/Login.gd" id="1_ff23o"]

[node name="Login" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_ff23o")

[node name="PanelContainer" type="PanelContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -175.0
offset_top = -125.0
offset_right = 175.0
offset_bottom = 125.0
grow_horizontal = 2
grow_vertical = 2

[node name="MarginContainer" type="MarginContainer" parent="PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="VBoxContainer" type="VBoxContainer" parent="PanelContainer/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="TitleLabel" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "LOGIN"
horizontal_alignment = 1
uppercase = true

[node name="LoginContainer" type="VBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_constants/separation = 10

[node name="HBoxContainer" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="LabelNickname" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer"]
custom_minimum_size = Vector2(90, 0)
layout_mode = 2
text = "Email"

[node name="InputNickname" type="LineEdit" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Enter your email"

[node name="HBoxContainer2" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="LabelPassword" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer2"]
custom_minimum_size = Vector2(90, 0)
layout_mode = 2
text = "Password"

[node name="InputPassword" type="LineEdit" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer2"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Enter your password"
secret = true

[node name="HBoxContainer4" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer"]
layout_mode = 2

[node name="RememberMeCheckBox" type="CheckBox" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer4"]
unique_name_in_owner = true
layout_mode = 2
text = "Remember Me"

[node name="LoginButton" type="Button" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer"]
layout_mode = 2
text = "Login"

[node name="HBoxContainer3" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer"]
layout_mode = 2

[node name="ForgotPasswordButton" type="LinkButton" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer3"]
layout_mode = 2
text = "Forgot Password?"

[node name="Spacer" type="Control" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3

[node name="RegisterButton" type="LinkButton" parent="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer3"]
layout_mode = 2
text = "Create Account"

[node name="RegisterContainer" type="VBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
visible = false
layout_mode = 2
theme_override_constants/separation = 10

[node name="HBoxContainer" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="LabelEmail" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/HBoxContainer"]
custom_minimum_size = Vector2(90, 0)
layout_mode = 2
text = "Email"

[node name="InputRegEmail" type="LineEdit" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Enter your email"

[node name="HBoxContainer2" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="LabelNickname" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/HBoxContainer2"]
custom_minimum_size = Vector2(90, 0)
layout_mode = 2
text = "Nickname"

[node name="InputRegNickname" type="LineEdit" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/HBoxContainer2"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Enter your nickname"

[node name="HBoxContainer3" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="LabelPassword" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/HBoxContainer3"]
custom_minimum_size = Vector2(90, 0)
layout_mode = 2
text = "Password"

[node name="InputRegPassword" type="LineEdit" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/HBoxContainer3"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Enter your password"
secret = true

[node name="HBoxContainer4" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="LabelGamePass" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/HBoxContainer4"]
custom_minimum_size = Vector2(90, 0)
layout_mode = 2
text = "Game Pass"
autowrap_mode = 2

[node name="InputRegGamePassword" type="LineEdit" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/HBoxContainer4"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Enter game password"
secret = true

[node name="RegisterButton" type="Button" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer"]
layout_mode = 2
text = "Register"

[node name="BackToLoginButton" type="LinkButton" parent="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer"]
layout_mode = 2
text = "Back to Login"

[node name="ForgotPasswordContainer" type="VBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
visible = false
layout_mode = 2
theme_override_constants/separation = 10

[node name="HBoxContainer" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer/ForgotPasswordContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="LabelEmail" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer/ForgotPasswordContainer/HBoxContainer"]
custom_minimum_size = Vector2(90, 0)
layout_mode = 2
text = "Email"

[node name="InputResetEmail" type="LineEdit" parent="PanelContainer/MarginContainer/VBoxContainer/ForgotPasswordContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Enter your email"

[node name="ResetPasswordButton" type="Button" parent="PanelContainer/MarginContainer/VBoxContainer/ForgotPasswordContainer"]
layout_mode = 2
text = "Reset Password"

[node name="BackToLoginButton" type="LinkButton" parent="PanelContainer/MarginContainer/VBoxContainer/ForgotPasswordContainer"]
layout_mode = 2
text = "Back to Login"

[node name="ErrorLabel" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_colors/font_color = Color(1, 0, 0, 1)
text = "Error message placeholder"
autowrap_mode = 2

[connection signal="pressed" from="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/LoginButton" to="." method="_on_login_button_pressed"]
[connection signal="pressed" from="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer3/ForgotPasswordButton" to="." method="_on_forgot_password_pressed"]
[connection signal="pressed" from="PanelContainer/MarginContainer/VBoxContainer/LoginContainer/HBoxContainer3/RegisterButton" to="." method="_on_switch_to_register_pressed"]
[connection signal="pressed" from="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/RegisterButton" to="." method="_on_register_button_pressed"]
[connection signal="pressed" from="PanelContainer/MarginContainer/VBoxContainer/RegisterContainer/BackToLoginButton" to="." method="_on_switch_to_login_pressed"]
[connection signal="pressed" from="PanelContainer/MarginContainer/VBoxContainer/ForgotPasswordContainer/ResetPasswordButton" to="." method="_on_reset_password_button_pressed"]
[connection signal="pressed" from="PanelContainer/MarginContainer/VBoxContainer/ForgotPasswordContainer/BackToLoginButton" to="." method="_on_switch_to_login_pressed"]
