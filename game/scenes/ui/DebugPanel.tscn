[gd_scene load_steps=2 format=3 uid="uid://b1a2b3c4d5e6"]

[ext_resource type="Script" uid="uid://dxwcjobxn8mh2" path="res://scripts/ui/DebugPanel.gd" id="1_abcde"]

[node name="DebugPanel" type="PanelContainer"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -170.0
offset_top = -203.0
offset_right = 130.0
offset_bottom = 203.0
grow_vertical = 2
script = ExtResource("1_abcde")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 8

[node name="SimulateCheckBox" type="CheckBox" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Simulate GPS"

[node name="HSeparator3" type="HSeparator" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="Label3" type="Label" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Search Location:"

[node name="AddressEdit" type="LineEdit" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
placeholder_text = "e.g., Eiffel Tower, Paris"

[node name="SearchButton" type="Button" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Search Location"

[node name="HSeparator" type="HSeparator" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Simulated Latitude:"

[node name="LatitudeEdit" type="LineEdit" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
placeholder_text = "-90.0 to 90.0"
editable = false

[node name="Label2" type="Label" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Simulated Longitude:"

[node name="LongitudeEdit" type="LineEdit" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
placeholder_text = "-180.0 to 180.0"
editable = false

[node name="ApplyButton" type="Button" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
disabled = true
text = "Apply Simulated Location"

[node name="HSeparator2" type="HSeparator" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="Label4" type="Label" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Map Position Selection:"

[node name="SelectPositionButton" type="Button" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Select Position on Map"

[node name="PositionStatusLabel" type="Label" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Click the button to select a position"
autowrap_mode = 2

[node name="HSeparator4" type="HSeparator" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="StatusLabel" type="Label" parent="MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Status: Initializing..."
autowrap_mode = 2

[node name="GeocodingRequest" type="HTTPRequest" parent="."]

[connection signal="pressed" from="MarginContainer/VBoxContainer/SearchButton" to="." method="_on_search_button_pressed"]
[connection signal="pressed" from="MarginContainer/VBoxContainer/SelectPositionButton" to="." method="_on_select_position_button_pressed"]
[connection signal="request_completed" from="GeocodingRequest" to="." method="_on_geocoding_request_completed"]
