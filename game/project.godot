; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Koku<PERSON>"
config/version="0.0.1"
run/main_scene="res://scenes/main/Boot.tscn"
config/features=PackedStringArray("4.4", "GL Compatibility")
config/icon="res://icon.svg"

[autoload]

KokumeApi="*res://scripts/autoload/KokumeApi.gd"
GpsService="*res://scripts/autoload/GpsService.gd"
GameState="*res://scripts/autoload/GameState.gd"
UIManager="*res://scripts/autoload/UIManager.gd"
Logger="*res://scripts/autoload/Logger.gd"

[display]

window/size/viewport_width=430
window/size/viewport_height=932
window/handheld/orientation=1

[editor_plugins]

enabled=PackedStringArray("res://addons/PraxisMapperGPSPlugin/plugin.cfg")

[logging]

enabled=true
min_level="INFO"
default_source_enabled=true

[logging.sources]

API/enabled=true
GPS/enabled=true
UI/enabled=true
Map/enabled=true
GameState/enabled=true
Player/enabled=true

[rendering]

renderer/rendering_method="gl_compatibility"
renderer/rendering_method.mobile="gl_compatibility"
textures/vram_compression/import_etc2_astc=true
