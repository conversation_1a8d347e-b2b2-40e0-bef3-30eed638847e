extends Node

# Test script for touch controls on Map.gd
# This script can be used to verify that touch gestures work correctly

func _ready():
	print("Touch Controls Test Script Loaded")
	print("Testing touch gesture functionality...")
	
	# Find the map instance
	var map = get_tree().get_first_node_in_group("Map")
	if not map:
		print("ERROR: Map not found in scene tree!")
		return
	
	print("Map found: ", map.name)
	print("Map class: ", map.get_class())
	
	# Test if map has the new touch-related variables
	test_map_variables(map)
	
	# Test gesture functions exist
	test_gesture_functions(map)


func test_map_variables(map):
	print("\n=== Testing Map Variables ===")
	
	# Check if new variables exist (they should be accessible via get())
	var variables_to_check = [
		"_active_touches",
		"_is_pinching", 
		"_momentum_velocity",
		"_pinch_start_distance",
		"_pinch_start_zoom"
	]
	
	for var_name in variables_to_check:
		if map.has_method("get") and map.get(var_name) != null:
			print("✓ Variable %s exists" % var_name)
		else:
			print("✗ Variable %s missing or null" % var_name)


func test_gesture_functions(map):
	print("\n=== Testing Gesture Functions ===")
	
	var functions_to_check = [
		"handle_magnify_gesture",
		"handle_pan_gesture", 
		"_start_pinch_gesture",
		"_update_pinch_gesture",
		"_start_momentum_scrolling",
		"_update_momentum"
	]
	
	for func_name in functions_to_check:
		if map.has_method(func_name):
			print("✓ Function %s exists" % func_name)
		else:
			print("✗ Function %s missing" % func_name)


func simulate_touch_events(map):
	print("\n=== Simulating Touch Events ===")
	
	# Create a simple touch event
	var touch_event = InputEventScreenTouch.new()
	touch_event.index = 0
	touch_event.position = Vector2(100, 100)
	touch_event.pressed = true
	
	print("Simulating touch press at (100, 100)")
	map.handle_screen_touch(touch_event)
	
	# Simulate drag
	var drag_event = InputEventScreenDrag.new()
	drag_event.index = 0
	drag_event.position = Vector2(150, 150)
	
	print("Simulating drag to (150, 150)")
	map.handle_screen_drag(drag_event)
	
	# Simulate release
	touch_event.pressed = false
	touch_event.position = Vector2(150, 150)
	
	print("Simulating touch release at (150, 150)")
	map.handle_screen_touch(touch_event)
	
	print("Touch simulation complete")


# Call this function to run the full test
func run_full_test():
	var map = get_tree().get_first_node_in_group("Map")
	if map:
		simulate_touch_events(map)
	else:
		print("Cannot run simulation - Map not found")
