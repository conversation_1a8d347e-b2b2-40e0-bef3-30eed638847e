extends "res://scripts/ui/Modal.gd" # Inherit from the base Modal script

# References to specific nodes within this modal's content
@onready var help_text_label: RichTextLabel = %HelpTextLabel # Use RichTextLabel for potential formatting

func _ready():
	super._ready() # Call the base class's ready function
	# Set the title specifically for this modal
	get_node(^"MarginContainer/VBoxContainer/TitleLabel").text = "Help"
	# Load help content (can be from a file or hardcoded)
	_load_help_content()


# Called when the modal needs to be shown
func open_modal():
	# Content is loaded in _ready, just open it
	super.open_modal()


func _load_help_content():
	if not help_text_label:
		Logger.error("UI", "HelpModal: HelpTextLabel node not found!")
		return

	# Example: Load from a simple text file or use BBCode directly
	# For now, using placeholder BBCode
	help_text_label.bbcode_enabled = true
	help_text_label.text = """
[center][b]Kokume Help[/b][/center]

[b]Gameplay:[/b]
Explore the real world using your device's GPS. Find resource nodes and capture territories displayed on the map.

[b]Controls:[/b]
- [b]Pan:[/b] Drag the map with your finger or mouse.
- [b]Zoom:[/b] Pinch with two fingers or use the mouse wheel.
- [b]Capture:[/b] Move close to an unowned or enemy territory and press the 'Capture Territory' button.
- [b]Gather:[/b] Move close to a resource node and press the 'Gather Resource' button.

[b]Goal:[/b]
Expand your influence by capturing territories and managing resources!
"""
	# Adjust scroll following behavior if needed
	help_text_label.scroll_following = true


# Override close_modal if specific actions are needed when help is closed
# func close_modal():
#	 super.close_modal()
#	 # Add specific closing logic here if necessary
