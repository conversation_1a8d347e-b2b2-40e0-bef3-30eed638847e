extends PanelContainer

# Signal emitted when the modal is closed
signal closed

@onready var title_label: Label = %TitleLabel
@onready var close_button: Button = %CloseButton
@onready var content_container: Container = %ContentContainer # Where specific modal content goes

func _ready():
	# Hide by default
	visible = false
	# Connect close button if it exists
	if close_button and close_button is Button:
		close_button.pressed.connect(close_modal)
	else:
		Logger.debug("UI", "Modal: Optional CloseButton not found.")

	# Apply responsive sizing based on screen size
	_apply_responsive_sizing()


func open_modal():
	# Optional: Add animations or logic before showing
	visible = true
	# Optional: Pause game elements if needed
	# get_tree().paused = true


func open_and_fill_modal(data: Dictionary):
	Logger.warning("UI", "Modal::open_and_fill_modal is empty")
	open_modal()


func close_modal():
	# Optional: Add animations or logic before hiding
	visible = false
	closed.emit()
	# Optional: Resume game elements if needed
	# get_tree().paused = false

# Function to apply responsive sizing based on screen size
func _apply_responsive_sizing():
	var screen_size = DisplayServer.window_get_size()
	Logger.debug("UI", "Applying responsive sizing. Screen size: %s" % screen_size)

	# Get top bar height to avoid overlap
	var top_bar_height = 100.0 # Default height of top bar

	# Adjust margins based on screen size
	if screen_size.x < 600: # Small phone
		anchors_preset = 15 # Full screen
		anchor_right = 1.0
		anchor_bottom = 1.0
		offset_left = 10.0
		offset_right = -10.0
		offset_top = top_bar_height + 10.0 # Avoid overlap with top bar
		offset_bottom = -10.0
	elif screen_size.x < 1000: # Large phone or small tablet
		anchors_preset = 15 # Full screen
		anchor_right = 1.0
		anchor_bottom = 1.0
		offset_left = 20.0
		offset_right = -20.0
		offset_top = top_bar_height + 10.0 # Avoid overlap with top bar
		offset_bottom = -20.0
	else: # Tablet or desktop
		# Center with fixed size for larger screens
		anchors_preset = 8 # Center
		anchor_left = 0.5
		anchor_top = 0.5
		anchor_right = 0.5
		anchor_bottom = 0.5
		offset_left = -300.0
		offset_top = -200.0
		offset_right = 300.0
		offset_bottom = 200.0

# Optional: Function to set content dynamically if needed
# func set_content(content_node: Node):
#	 if content_container:
#		 # Clear previous content
#		 for child in content_container.get_children():
#			 content_container.remove_child(child)
#			 child.queue_free()
#		 # Add new content
#		 content_container.add_child(content_node)
#	 else:
#		 printerr("Modal: ContentContainer node not found to set content.")
